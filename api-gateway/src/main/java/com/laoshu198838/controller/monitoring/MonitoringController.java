package com.laoshu198838.controller.monitoring;

import com.laoshu198838.config.CustomMetricsConfig;
import com.laoshu198838.util.MetricsHelper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 监控管理控制器
 * 提供应用监控和指标管理的API接口
 *
 * <AUTHOR>
 */
@Tag(name = "监控管理", description = "应用监控和指标管理相关API")
@RestController
@RequestMapping("/api/admin/monitoring")
@CrossOrigin(origins = {"${app.cors.allowed-origins:http://localhost:3000,http://localhost:5173}"}, allowCredentials = "true")
public class MonitoringController {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired(required = false)
    private CustomMetricsConfig customMetricsConfig;

    @Autowired(required = false)
    private Counter userLoginCounter;

    @Autowired(required = false)
    private Counter userLoginFailureCounter;

    @Autowired(required = false)
    private Counter debtQueryCounter;

    @Autowired(required = false)
    private Counter reportGenerationCounter;

    @Autowired(required = false)
    private Counter dataExportCounter;

    @Autowired(required = false)
    private Counter cacheHitCounter;

    @Autowired(required = false)
    private Counter cacheMissCounter;

    @Autowired(required = false)
    private Timer databaseQueryTimer;

    @Autowired(required = false)
    private Timer apiResponseTimer;

    /**
     * 获取应用指标概览
     */
    @Operation(summary = "获取应用指标概览", description = "获取应用的关键性能指标概览")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/metrics/overview")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getMetricsOverview() {
        try {
            Map<String, Object> overview = new HashMap<>();

            // 业务指标
            Map<String, Object> businessMetrics = new HashMap<>();
            businessMetrics.put("userLogins", MetricsHelper.safeCount(userLoginCounter));
            businessMetrics.put("userLoginFailures", MetricsHelper.safeCount(userLoginFailureCounter));
            businessMetrics.put("debtQueries", MetricsHelper.safeCount(debtQueryCounter));
            businessMetrics.put("reportGenerations", MetricsHelper.safeCount(reportGenerationCounter));
            businessMetrics.put("dataExports", MetricsHelper.safeCount(dataExportCounter));

            // 缓存指标
            Map<String, Object> cacheMetrics = new HashMap<>();
            double hits = MetricsHelper.safeCount(cacheHitCounter);
            double misses = MetricsHelper.safeCount(cacheMissCounter);
            cacheMetrics.put("hits", hits);
            cacheMetrics.put("misses", misses);
            double hitRate = MetricsHelper.calculateHitRate(hits, misses);
            cacheMetrics.put("hitRate", String.format("%.2f%%", hitRate));

            // 性能指标
            Map<String, Object> performanceMetrics = new HashMap<>();
            performanceMetrics.put("avgDatabaseQueryTime", databaseQueryTimer.mean(TimeUnit.MILLISECONDS));
            performanceMetrics.put("avgApiResponseTime", apiResponseTimer.mean(TimeUnit.MILLISECONDS));
            performanceMetrics.put("totalDatabaseQueries", MetricsHelper.safeCount(databaseQueryTimer));
            performanceMetrics.put("totalApiRequests", MetricsHelper.safeCount(apiResponseTimer));

            // 系统指标
            Map<String, Object> systemMetrics = new HashMap<>();
            Runtime runtime = Runtime.getRuntime();
            systemMetrics.put("memoryUsed", runtime.totalMemory() - runtime.freeMemory());
            systemMetrics.put("memoryTotal", runtime.totalMemory());
            systemMetrics.put("memoryMax", runtime.maxMemory());
            systemMetrics.put("processors", runtime.availableProcessors());

            overview.put("business", businessMetrics);
            overview.put("cache", cacheMetrics);
            overview.put("performance", performanceMetrics);
            overview.put("system", systemMetrics);
            overview.put("success", true);

            return ResponseEntity.ok(overview);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 获取详细的业务指标
     */
    @Operation(summary = "获取详细的业务指标", description = "获取详细的业务相关指标")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/metrics/business")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getBusinessMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();

            // 用户相关指标
            Map<String, Object> userMetrics = new HashMap<>();
            double totalLogins = MetricsHelper.safeCount(userLoginCounter);
            double failedLogins = MetricsHelper.safeCount(userLoginFailureCounter);
            userMetrics.put("totalLogins", totalLogins);
            userMetrics.put("failedLogins", failedLogins);
            userMetrics.put("successRate", MetricsHelper.calculateSuccessRate(totalLogins, failedLogins));

            // 债务相关指标
            Map<String, Object> debtMetrics = new HashMap<>();
            debtMetrics.put("totalQueries", MetricsHelper.safeCount(debtQueryCounter));

            // 报表相关指标
            Map<String, Object> reportMetrics = new HashMap<>();
            reportMetrics.put("totalGenerations", MetricsHelper.safeCount(reportGenerationCounter));

            // 数据导出指标
            Map<String, Object> exportMetrics = new HashMap<>();
            exportMetrics.put("totalExports", MetricsHelper.safeCount(dataExportCounter));

            metrics.put("users", userMetrics);
            metrics.put("debts", debtMetrics);
            metrics.put("reports", reportMetrics);
            metrics.put("exports", exportMetrics);
            metrics.put("success", true);

            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 刷新业务指标
     */
    @Operation(summary = "刷新业务指标", description = "手动刷新业务指标数据")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "刷新成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/metrics/refresh")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> refreshMetrics() {
        try {
            customMetricsConfig.updateBusinessMetrics();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "业务指标已刷新");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 获取性能指标
     */
    @Operation(summary = "获取性能指标", description = "获取应用性能相关指标")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "403", description = "权限不足"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/metrics/performance")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getPerformanceMetrics() {
        try {
            Map<String, Object> metrics = new HashMap<>();

            // 数据库性能
            Map<String, Object> dbMetrics = new HashMap<>();
            dbMetrics.put("avgQueryTime", databaseQueryTimer.mean(TimeUnit.MILLISECONDS));
            dbMetrics.put("maxQueryTime", databaseQueryTimer.max(TimeUnit.MILLISECONDS));
            dbMetrics.put("totalQueries", MetricsHelper.safeCount(databaseQueryTimer));

            // API性能
            Map<String, Object> apiMetrics = new HashMap<>();
            apiMetrics.put("avgResponseTime", apiResponseTimer.mean(TimeUnit.MILLISECONDS));
            apiMetrics.put("maxResponseTime", apiResponseTimer.max(TimeUnit.MILLISECONDS));
            apiMetrics.put("totalRequests", MetricsHelper.safeCount(apiResponseTimer));

            // 缓存性能
            Map<String, Object> cacheMetrics = new HashMap<>();
            cacheMetrics.put("hits", MetricsHelper.safeCount(cacheHitCounter));
            cacheMetrics.put("misses", MetricsHelper.safeCount(cacheMissCounter));
            cacheMetrics.put("hitRate", calculateHitRate());

            metrics.put("database", dbMetrics);
            metrics.put("api", apiMetrics);
            metrics.put("cache", cacheMetrics);
            metrics.put("success", true);

            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    /**
     * 计算成功率
     */
    private String calculateSuccessRate(double success, double failure) {
        double total = success + failure;
        if (total == 0) return "0.00%";
        return String.format("%.2f%%", (success / total) * 100);
    }

    /**
     * 计算缓存命中率
     */
    private String calculateHitRate() {
        double hits = MetricsHelper.safeCount(cacheHitCounter);
        double misses = MetricsHelper.safeCount(cacheMissCounter);
        double total = hits + misses;
        if (total == 0) return "0.00%";
        return String.format("%.2f%%", (hits / total) * 100);
    }
}
