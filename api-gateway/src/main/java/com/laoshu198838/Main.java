package com.laoshu198838;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.laoshu198838.config.UserSystemDataSourceConfig;
import com.laoshu198838.config.UserSystemJpaConfig;


/**
 * 财务信息化系统主启动类
 *
 * <p>本系统是一个基于Spring Boot的多模块财务管理系统，主要用于处理逾期债权数据管理、
 * 金蝶系统集成、用户权限管理、数据一致性检查等业务功能。</p>
 *
 * <h3>系统架构：</h3>
 * <ul>
 *   <li><strong>公共模块 (common)</strong>: 包含实体类、DTO、枚举类、工具类等共享组件</li>
 *   <li><strong>数据访问层 (data-access)</strong>: 包含Repository接口、多数据源配置、动态数据源切换</li>
 *   <li><strong>业务模块组 (business-modules)</strong>: 包含各业务领域的服务类
 *     <ul>
 *       <li>债权管理 (debt-management): 逾期债权数据处理、诉讼/非诉讼债权管理</li>
 *       <li>账户管理 (account-management): 用户账户管理、角色权限控制</li>
 *       <li>审计管理 (audit-management): 数据审计、操作日志记录</li>
 *       <li>报表管理 (report-management): 财务报表生成、数据导出</li>
 *     </ul>
 *   </li>
 *   <li><strong>Web服务层 (webservice)</strong>: 包含REST API控制器、安全配置、过滤器等</li>
 * </ul>
 *
 * <h3>技术栈：</h3>
 * <ul>
 *   <li>Spring Boot 3.1.2 - 主框架</li>
 *   <li>Spring Security - 安全认证</li>
 *   <li>Spring Data JPA - 数据访问</li>
 *   <li>MySQL 8.0 - 数据库</li>
 *   <li>HikariCP - 连接池</li>
 *   <li>JWT - 令牌认证</li>
 *   <li>Maven - 项目管理</li>
 * </ul>
 *
 * <h3>数据库配置：</h3>
 * <ul>
 *   <li>主数据源: overdue_debt_db - 存储核心业务数据</li>
 *   <li>第二数据源: kingdee数据库 - 金蝶系统集成数据</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.laoshu198838.controller",
        "com.laoshu198838.service",
        "com.laoshu198838.entity",
        "com.laoshu198838.repository",
        "com.laoshu198838.config",
        "com.laoshu198838.util",
        "com.laoshu198838.oa",
        "com.laoshu198838.security",
        "com.laoshu198838.template"
    },
    scanBasePackageClasses = {
        com.laoshu198838.config.UserSystemDataSourceConfig.class,
        com.laoshu198838.config.UserSystemJpaConfig.class
    },
    exclude = {
        org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration.class,
        org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class
    }
)
@Import({UserSystemDataSourceConfig.class, UserSystemJpaConfig.class})
@EnableScheduling // 启用Spring定时任务 - autoCommit冲突已解决，重新启用
@EnableAspectJAutoProxy // 启用AspectJ自动代理，支持AOP切面
public class Main {

    /**
     * 日志记录器，用于记录系统启动过程和状态信息
     */
    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    /**
     * 系统主入口方法
     *
     * <p>负责启动整个财务信息化系统，包括：</p>
     * <ul>
     *   <li>初始化Spring Boot应用上下文</li>
     *   <li>加载所有模块的组件和配置</li>
     *   <li>启动Web服务器（默认端口8080）</li>
     *   <li>初始化数据库连接池</li>
     *   <li>启动定时任务调度器</li>
     *   <li>加载安全配置和JWT认证</li>
     * </ul>
     *
     * <p>系统启动后会自动执行以下初始化任务：</p>
     * <ul>
     *   <li>数据库连接测试</li>
     *   <li>定时数据更新任务启动</li>
     *   <li>用户权限系统初始化</li>
     *   <li>业务服务组件加载</li>
     * </ul>
     *
     * @param args 命令行参数，支持Spring Boot标准参数
     *             如：--server.port=8081 修改端口
     *                --spring.profiles.active=dev 激活开发环境配置
     */
    public static void main(String[] args) {
        // 记录系统启动开始
        logger.info("🚀 财务信息化系统启动中...");

        // Spring Boot应用启动入口
        // 这里会初始化所有的Bean、配置类、数据源等
        SpringApplication.run(Main.class, args);

        // 记录系统启动完成
        logger.info("✅ 财务信息化系统启动完成 - 端口:8080");
    }
}