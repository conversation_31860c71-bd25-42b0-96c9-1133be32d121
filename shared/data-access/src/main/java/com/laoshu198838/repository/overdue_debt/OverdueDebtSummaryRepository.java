package com.laoshu198838.repository.overdue_debt;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary;
import com.laoshu198838.entity.overdue_debt.OverdueDebtSummary.OverdueDebtSummaryKey;

/**
 * 逾期债权汇总数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OverdueDebtSummaryRepository extends JpaRepository<OverdueDebtSummary, OverdueDebtSummaryKey> {

    // 获取本年处置债权金额
    @Query(value = "SELECT COALESCE(SUM(本年减少债权金额), 0) FROM 汇总表", nativeQuery = true)
    Double findTotalReductionAmount();

    // 获取期末债权余额
    @Query(value = "SELECT COALESCE(SUM(本年期末债权余额), 0) FROM 汇总表", nativeQuery = true)
    Double findTotalDebtBalance();

    // 获取年初逾期债权余额
    @Query(value = "SELECT COALESCE(SUM(年初逾期债权余额), 0) FROM 汇总表", nativeQuery = true)
    Double findInitialDebtBalance();

    // 获取存量债权处置金额
    @Query(value = """
                   SELECT
                       (COALESCE((SELECT SUM(本年减少债权金额) FROM 汇总表), 0) -
                        COALESCE((SELECT SUM(处置金额) FROM 新增表), 0)) AS result;""", nativeQuery = true)
    Double findInitialDebtReductionAmount();

    // 获取存量债权期末余额
    @Query(value = """
                   SELECT
                       (COALESCE((SELECT SUM(本年期末债权余额) FROM 汇总表), 0) -
                        COALESCE((SELECT SUM(债权余额) FROM 新增表), 0)) AS result;""", nativeQuery = true)
    Double findInitialDebtEndingBalance();

    // 获取新增债权金额
    @Query(value = "SELECT COALESCE(SUM(新增金额), 0) FROM 新增表", nativeQuery = true)
    Double findNewDebtAmount();

    // 获取新增减少债权金额
    @Query(value = "SELECT COALESCE(SUM(处置金额), 0) FROM 新增表", nativeQuery = true)
    Double findNewDebtReductionAmount();

    //  获得存量债权处置金额=本年处置金额-新增处置金额
    @Query(value = "SELECT COALESCE(SUM(债权余额), 0) FROM 新增表", nativeQuery = true)
    Double findNewDebtBalance();

    @Query(value = """
                       SELECT
                           管理公司 AS companyName,
                           SUM(IFNULL(新增金额, 0)) AS newAmountSum,
                           SUM(IFNULL(处置金额, 0)) AS reductionAmountSum
                       FROM
                           新增表
                       WHERE
                           (:year IS NULL OR 年份 = :year)
                       GROUP BY
                           管理公司
                   """, nativeQuery = true)
    List<Map<String, Object>> findNewDebtSummaryByCompany(@Param("year") String year);

    @Query(value = """
                       SELECT
                         sub1.companyName,
                         sub1.newAmountSum,
                         (sub1.totalReduction - COALESCE(sub2.disposalSum, 0)) AS reductionAmountSum
                       FROM
                       (
                         SELECT
                           管理公司 AS companyName,
                           SUM(`年初逾期债权余额`)   AS newAmountSum,
                           SUM(`本年减少债权金额`)   AS totalReduction
                         FROM `汇总表`
                         WHERE
                           (:year IS NULL OR year = :year)
                         GROUP BY 管理公司
                       ) AS sub1
                       LEFT JOIN
                       (
                         SELECT
                           管理公司 AS companyName,
                           SUM(`处置金额`) AS disposalSum
                         FROM `新增表`
                         WHERE
                           (:year IS NULL OR 年份 = :year)
                         GROUP BY 管理公司
                       ) AS sub2
                         ON sub1.companyName = sub2.companyName
                       WHERE
                         NOT (
                           sub1.newAmountSum = 0
                           AND (sub1.totalReduction - COALESCE(sub2.disposalSum, 0)) = 0
                         )
                   """, nativeQuery = true)
    List<Map<String, Object>> findExistingDebtSummaryByCompany(@Param("year") String year);

    //    从处置表里面查询月份汇总数据（管理公司月度新增处置情况表）
    @Query(value = """
                              SELECT
                                  COALESCE(a.管理公司, b.管理公司) AS companyName,
                                  IFNULL(a.新增月份金额, 0) AS newAmount,
                                  IFNULL(b.处置月份金额, 0) AS reductionAmount
                              FROM (
                                  SELECT
                                      管理公司,
                                      SUM(
                                          CASE :month
                                              WHEN '1月' THEN 1月
                                              WHEN '2月' THEN 2月
                                              WHEN '3月' THEN 3月
                                              WHEN '4月' THEN 4月
                                              WHEN '5月' THEN 5月
                                              WHEN '6月' THEN 6月
                                              WHEN '7月' THEN 7月
                                              WHEN '8月' THEN 8月
                                              WHEN '9月' THEN 9月
                                              WHEN '10月' THEN 10月
                                              WHEN '11月' THEN 11月
                                              WHEN '12月' THEN 12月
                                          END
                                      ) AS 新增月份金额
                                  FROM 新增表
                                  WHERE
                                      年份 = :year
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                                  GROUP BY 管理公司
                              ) a
                              LEFT JOIN (
                                  SELECT
                                      管理公司,
                                      SUM(每月处置金额) AS 处置月份金额
                                  FROM 处置表
                                  WHERE
                                      年份 = :year
                                      AND CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 月份
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                                  GROUP BY 管理公司
                              ) b ON a.管理公司 = b.管理公司

                              UNION

                              SELECT
                                  b.管理公司,
                                  0 AS newAmount,
                                  b.处置月份金额 AS reductionAmount
                              FROM (
                                  SELECT
                                      管理公司,
                                      SUM(每月处置金额) AS 处置月份金额
                                  FROM 处置表
                                  WHERE
                                      年份 = :year
                                      AND CAST(SUBSTRING(:month, 1, LENGTH(:month) - 1) AS SIGNED) = 月份
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                                  GROUP BY 管理公司
                              ) b
                              WHERE b.管理公司 NOT IN (
                                  SELECT 管理公司
                                  FROM 新增表
                                  WHERE
                                      年份 = :year
                                      AND (:company = '所有公司' OR 管理公司 = :company)
                              )
                              ORDER BY companyName
                   """, nativeQuery = true)
    List<Map<String, Object>> findMonthNewReductionDebtByCompany(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);

    @Query(value = """
                           SELECT
                               管理公司,
                               债权人,
                               债务人,
                               新增金额,
                               处置金额,
                               债权余额
                           FROM
                               新增表
                           WHERE
                               (:year IS NULL OR 年份 = :year)
                               AND (:company IS NULL OR :company = '所有公司' OR :company = '全部' OR 管理公司 = :company)
                               AND (
                                   :month IS NULL
                                   OR :month = '所有月份'
                                   OR :month = '全部'
                                   OR (
                                       :month = '1月' AND 1月 > 0
                                       OR :month = '2月' AND 2月 > 0
                                       OR :month = '3月' AND 3月 > 0
                                       OR :month = '4月' AND 4月 > 0
                                       OR :month = '5月' AND 5月 > 0
                                       OR :month = '6月' AND 6月 > 0
                                       OR :month = '7月' AND 7月 > 0
                                       OR :month = '8月' AND 8月 > 0
                                       OR :month = '9月' AND 9月 > 0
                                       OR :month = '10月' AND 10月 > 0
                                       OR :month = '11月' AND 11月 > 0
                                       OR :month = '12月' AND 12月 > 0
                                   )
                               )
                           ORDER BY 新增金额 DESC
                   """, nativeQuery = true)
    List<Map<String, Object>> findNewDebtDetailList(
            @Param("year") String year,
            @Param("month") String month,
            @Param("company") String company);
}
