package com.laoshu198838.util.file;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aspose.cells.Cell;
import com.aspose.cells.CellValueType;
import com.aspose.cells.Cells;
import com.aspose.cells.Style;
import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.laoshu198838.config.AsposeLicenseConfig;

/**
 * Excel文件操作工具类
 *
 * <p>提供Excel文件读取、写入、保存等通用工具方法。</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class ExcelUtils {

    // 静态初始化块，确保在使用 Aspose 之前加载许可证
    static {
        AsposeLicenseConfig.ensureLicenseLoaded();
    }

    /**
     * 私有构造函数，防止实例化工具类
     */
    private ExcelUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // 常量定义
    private static final String EXCEL_EXTENSION = ".xlsx";

    /**
     * 读取 Excel 文件并转换为 List<List<Object>> 格式
     *
     * <p>该方法会自动过滤掉全为零或空的行，只返回有效数据行。</p>
     *
     * @param filePath  Excel 文件的完整路径
     * @param sheetName 要读取的工作表名称（可选，默认为空，为空时读取第一个工作表）
     * @return 读取到的数据，List 的每一行代表 Excel 的一行数据，每一行中每列为 Object
     */
    public static List<List<Object>> readExcelToList(String filePath, String sheetName) {
        List<List<Object>> data = new ArrayList<>();
        try {
            // 加载 Excel 文件
            Workbook workbook = new Workbook(filePath);
            Worksheet worksheet;

            if (sheetName == null || sheetName.trim().isEmpty()) {
                // 如果未指定工作表名称，则读取第一个工作表
                worksheet = workbook.getWorksheets().get(0);
            } else {
                // 根据工作表名称获取工作表
                worksheet = workbook.getWorksheets().get(sheetName);
                if (worksheet == null) {
                    throw new IllegalArgumentException("Worksheet with name \"" + sheetName + "\" not found.");
                }
            }

            // 获取工作表的单元格集合
            Cells cells = worksheet.getCells();

            // 获取数据范围
            int maxRow = cells.getMaxDataRow();
            int maxColumn = cells.getMaxDataColumn();

            // 遍历每一行
            for (int row = 0; row <= maxRow; row++) {
                List<Object> rowData = new ArrayList<>();
                boolean isAllZeroOrNull = true; // 标记该行是否全为零或空

                // 遍历每一列
                for (int col = 0; col <= maxColumn; col++) {
                    Cell cell = cells.get(row, col);
                    Object cellValue = null;

                    if (cell != null) {
                        // 根据单元格类型获取值
                        switch (cell.getType()) {
                            case CellValueType.IS_NUMERIC:
                                cellValue = cell.getDoubleValue();
                                break;
                            case CellValueType.IS_STRING:
                                cellValue = cell.getStringValue();
                                break;
                            case CellValueType.IS_BOOL:
                                cellValue = cell.getBoolValue();
                                break;
                            case CellValueType.IS_DATE_TIME:
                                cellValue = cell.getDateTimeValue();
                                break;
                            default:
                                cellValue = cell.getValue();
                                break;
                        }
                    }

                    // 检查该单元格是否为非零非空值
                    if (cellValue != null && !cellValue.toString().trim().isEmpty() &&
                        !(cellValue instanceof Number && ((Number) cellValue).doubleValue() == 0)) {
                        isAllZeroOrNull = false;
                    }

                    // 将单元格值添加到行数据中
                    rowData.add(cellValue);
                }

                // 如果整行不全为零或空，添加到结果集
                if (!isAllZeroOrNull) {
                    data.add(rowData);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to read Excel file: " + filePath, e);
        }

        return data;
    }

    /**
     * 将字符串写入 Excel 单元格，如果是数字则写入数值，避免 Excel 将数字识别为文本。
     *
     * @param cell  要写入的单元格
     * @param value 待写入的字符串
     */
    public static void putValueWithFormat(Cell cell, String value) {
        // 先获取单元格样式
        Style style = cell.getStyle();
        // 取消可能的文本前缀，避免Excel将数值当作文本
        style.setQuotePrefix(false);

        if (value == null) {
            // 空值直接写空字符串
            cell.putValue("");
            cell.setStyle(style);
            return;
        }

        // 尝试解析为数值
        try {
            double numericValue = Double.parseDouble(value);
            cell.putValue(numericValue);
        } catch (NumberFormatException e) {
            // 如果不是数值，则作为字符串处理
            cell.putValue(value);
        }

        cell.setStyle(style);
    }

    /**
     * 加载 Excel 模板文件
     *
     * @param templateFileName 模板文件名（在 classpath 中）
     * @return 加载的 Workbook 对象
     * @throws Exception 如果文件加载失败
     */
    public static Workbook loadExcelTemplate(String templateFileName) throws Exception {
        System.out.println("🔍 正在加载Excel模板: " + templateFileName);

        // 尝试从classpath加载资源
        ClassLoader classLoader = ExcelUtils.class.getClassLoader();
        // 优先从templates目录查找模板文件
        String templatePath = "templates/" + templateFileName;
        java.io.InputStream inputStream = classLoader.getResourceAsStream(templatePath);
        
        // 如果templates目录下没有找到，则在根目录查找（向后兼容）
        if (inputStream == null) {
            inputStream = classLoader.getResourceAsStream(templateFileName);
        }

        if (inputStream == null) {
            System.err.println("❌ 无法在classpath中找到模板文件: " + templateFileName + "，尝试路径: templates/" + templateFileName + " 和 " + templateFileName);
            throw new IllegalArgumentException("Template file not found in classpath: " + templateFileName);
        }

        System.out.println("✅ 成功找到模板文件，正在加载...");

        try {
            // 使用InputStream创建Workbook
            Workbook workbook = new Workbook(inputStream);
            System.out.println("✅ Excel模板加载成功");
            return workbook;
        } catch (Exception e) {
            System.err.println("❌ 加载Excel模板失败: " + e.getMessage());
            throw new Exception("Failed to load Excel template: " + templateFileName, e);
        } finally {
            // 确保关闭输入流
            try {
                inputStream.close();
            } catch (Exception e) {
                System.err.println("⚠️ 关闭输入流时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 保存 Workbook 到指定路径
     *
     * @param workbook Workbook 对象
     * @param filePath 保存路径（可选，为空时保存到桌面）
     * @param filename 文件名（不含扩展名）
     * @throws Exception 如果保存失败
     */
    public static void saveWorkbook(Workbook workbook, String filePath, String filename) throws Exception {
        if (workbook == null) {
            throw new IllegalArgumentException("Workbook 不能为空");
        }

        // 如果未指定路径，则默认保存在桌面
        if (filePath == null || filePath.trim().isEmpty()) {
            filePath = System.getProperty("user.home") + File.separator + "Desktop" + File.separator + filename + EXCEL_EXTENSION;
        }

        // 确保目标目录存在
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // 保存 Excel 文件
        workbook.save(filePath + filename + EXCEL_EXTENSION);


    }

    /**
     * 保存 Workbook 到桌面（简化版本）
     *
     * @param workbook Workbook 对象
     * @param filename 文件名（不含扩展名）
     * @throws Exception 如果保存失败
     */
    public static void saveWorkbook(Workbook workbook, String filename) throws Exception {
        saveWorkbook(workbook, null, filename);
    }

    /**
     * 保存 Worksheet 到指定路径
     *
     * @param worksheet 需要保存的 Worksheet 对象
     * @param savePath  保存路径（可选，为空时保存到桌面）
     * @param fileName  生成的文件名（无需扩展名，默认 `.xlsx`）
     * @throws RuntimeException 如果保存失败
     */
    public static void saveWorkbook(Worksheet worksheet, String savePath, String fileName) {
        if (worksheet == null) {
            throw new IllegalArgumentException("Worksheet 不能为空");
        }

        // 如果未指定保存路径，默认保存到桌面
        if (savePath == null || savePath.trim().isEmpty()) {
            savePath = System.getProperty("user.home") + File.separator + "Desktop";
        }

        // 确保保存路径以文件分隔符结尾
        if (!savePath.endsWith(File.separator)) {
            savePath += File.separator;
        }

        // 构建完整的文件路径
        String fullPath = savePath + fileName + EXCEL_EXTENSION;

        try {
            // 获取 Worksheet 所属的 Workbook
            Workbook workbook = worksheet.getWorkbook();

            // 确保目标目录存在
            File file = new File(fullPath);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 保存文件
            workbook.save(fullPath);

            // Excel 已成功保存到：fullPath

        } catch (Exception e) {
            // 保存 Excel 失败：e.getMessage()
            throw new RuntimeException("保存 Excel 失败", e);
        }
    }

    /**
     * 保存 Worksheet 到桌面（简化版本）
     *
     * @param worksheet 需要保存的 Worksheet 对象
     * @param filename  文件名（不含扩展名）
     * @throws RuntimeException 如果保存失败
     */
    public static void saveWorkbook(Worksheet worksheet, String filename) {
        saveWorkbook(worksheet, null, filename);
    }

}
