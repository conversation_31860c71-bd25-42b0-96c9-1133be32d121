package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.LitigationClaim.LitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.model.overduedebt.dto.entity.LitigationClaimDTO;
import com.laoshu198838.model.overduedebt.dto.entity.OverdueDebtAddDTO;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.NonLitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtAddRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 服务类，处理新增逾期债权数据的保存与更新，同时根据涉诉情况分别更新诉讼、非诉讼和减值准备表。
 * <p>
 * 核心方法 {@link #addOverdueDebt(OverdueDebtAddDTO)} 根据前端传递的 DTO 数据更新新增表，同时调用相关方法更新其他表。
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class OverdueDebtAddService {

    /**
     * 年新增债权后缀
     */
    private static final String NEW_DEBT_SUFFIX = "年新增债权";

    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtAddService.class);

    private final OverdueDebtAddRepository overdueDebtAddRepository;
    private final NonLitigationClaimRepository nonLitigationClaimRepository;
    private final LitigationClaimRepository litigationClaimRepository;
    private final ImpairmentReserveRepository impairmentReserveRepository;

    public OverdueDebtAddService(OverdueDebtAddRepository overdueDebtAddRepository,
                                 NonLitigationClaimRepository nonLitigationClaimRepository,
                                 LitigationClaimRepository litigationClaimRepository,
                                 ImpairmentReserveRepository impairmentReserveRepository) {
        this.overdueDebtAddRepository = overdueDebtAddRepository;
        this.nonLitigationClaimRepository = nonLitigationClaimRepository;
        this.litigationClaimRepository = litigationClaimRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
    }

    /**
     * 新增逾期债权更新。根据前端提交的 DTO 数据，将数据映射并保存到新增表，同时根据是否涉诉更新相应的诉讼、非诉讼及减值准备表。
     *
     * @param dto 前端提交的逾期债权数据传输对象
     * @return 保存后的 OverdueDebtAdd 实体，包含数据库生成的主键等信息
     */
    @Transactional
    public OverdueDebtAdd addOverdueDebt(OverdueDebtAddDTO dto) {

        // （1）更新新增表
        OverdueDebtAdd entity = updateAddTable(dto);

        // （2）新增债权更新到减值准备表中
        updateImpairmentReserveTable(dto);

        // （3）新增债权更新到诉讼表中
        if ("是".equals(dto.getIsLitigation())) {
            updateLitigationClaimTable(dto);
        }

        // （4）新增债权更新到非诉讼表中
        if ("否".equals(dto.getIsLitigation())) {
            updateNonLitigationClaimTable(dto);
        }

        logger.info("新增表、诉讼表、非诉讼表和减值准备表保存完成: 债权人={}, 债务人={}, 期间={}", dto.getCreditor(), dto.getDebtor(), dto.getPeriod());

        return entity;
    }

    public OverdueDebtAdd updateAddTable(OverdueDebtAddDTO dto) {
        String newYearMonth = dto.getAddDate();
        String year = newYearMonth.split("-")[0];
        // 1. 查找匹配的记录
        OverdueDebtAdd entity = overdueDebtAddRepository.findByCreditorAndDebtorAndPeriodAndIsLitigationAndYear(
                dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), dto.getIsLitigation(), year);
        // 2. 根据是否存在记录判断更新还是新增
        if (entity == null) {
            // 2.1.未找到现有记录，将创建新记录
            entity = new OverdueDebtAdd();
            // 设置联合主键字段
            entity.setCreditor(dto.getCreditor());
            entity.setDebtor(dto.getDebtor());
            entity.setIsLitigation(dto.getIsLitigation());
            entity.setPeriod(dto.getPeriod());
            entity.setYear(year);

            // 设置其他字段
            entity.setManagementCompany(dto.getManagementCompany());
            entity.setRemark(dto.getMeasures());

            // 设置债权性质
            if (dto.getDebtNature() != null && !dto.getDebtNature().isEmpty()) {
                entity.setDebtNature(dto.getDebtNature());
            }
            // 设置科目名称
            if (dto.getSubjectName() != null && !dto.getSubjectName().isEmpty()) {
                entity.setSubjectName(dto.getSubjectName());
            }
            // 设置责任人
            if (dto.getResponsiblePerson() != null && !dto.getResponsiblePerson().isEmpty()) {
                entity.setResponsiblePerson(dto.getResponsiblePerson());
            }
            // 设置逾期日期/到期时间
            if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                try {
                    Date dueDate = parseDueDate(dto.getOverdueDate());
                    // 将Date格式转为字符串格式保存到dueTime字段
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    entity.setDueTime(sdf.format(dueDate));
                    logger.info("成功设置到期时间: {}", dto.getOverdueDate());
                } catch (ParseException e) {
                    logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                }
            }
            // 创建复合主键对象
            com.laoshu198838.entity.overdue_debt.OverdueDebtAdd.OverdueDebtAddKey compositeKey =
                    new com.laoshu198838.entity.overdue_debt.OverdueDebtAdd.OverdueDebtAddKey(dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), dto.getIsLitigation(), year);
            entity.setId(compositeKey);
        } else {
            logger.info("找到现有记录，将更新记录: ID={}", entity.getId());
            // 更新管理公司信息
            if (dto.getManagementCompany() != null && !dto.getManagementCompany().isEmpty()) {
                entity.setManagementCompany(dto.getManagementCompany());
            }
            // 更新债权性质
            if (dto.getDebtNature() != null && !dto.getDebtNature().isEmpty()) {
                entity.setDebtNature(dto.getDebtNature());
                logger.info("更新债权性质: {}", dto.getDebtNature());
            }
            // 更新科目名称
            if (dto.getSubjectName() != null && !dto.getSubjectName().isEmpty()) {
                entity.setSubjectName(dto.getSubjectName());
                logger.info("更新科目名称: {}", dto.getSubjectName());
            }
            // 更新逾期日期/到期时间
            if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                try {
                    Date dueDate = parseDueDate(dto.getOverdueDate());
                    // 将Date格式转为字符串格式保存到dueTime字段
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    entity.setDueTime(sdf.format(dueDate));
                    logger.info("更新到期时间: {}", dto.getOverdueDate());
                } catch (ParseException e) {
                    logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                }
            }
            // 更新责任人：在原有责任人基础上添加新责任人（避免重复）
            if (dto.getResponsiblePerson() != null && !dto.getResponsiblePerson().isEmpty()) {
                String currentResponsiblePerson = entity.getResponsiblePerson();
                if (currentResponsiblePerson != null && !currentResponsiblePerson.isEmpty()) {
                    if (!currentResponsiblePerson.contains(dto.getResponsiblePerson())) {
                        entity.setResponsiblePerson(currentResponsiblePerson + ", " + dto.getResponsiblePerson());
                        logger.info("更新责任人：原有[{}] + 新增[{}]", currentResponsiblePerson, dto.getResponsiblePerson());
                    } else {
                        logger.info("责任人未变化，原有责任人已包含新提交的责任人: {}", currentResponsiblePerson);
                    }
                } else {
                    entity.setResponsiblePerson(dto.getResponsiblePerson());
                    logger.info("设置责任人: {}", dto.getResponsiblePerson());
                }
            }
            // 更新处理措施/备注：采用追加方式，而非覆盖
            if (dto.getMeasures() != null && !dto.getMeasures().isEmpty()) {
                String currentRemark = entity.getRemark();
                if (currentRemark != null && !currentRemark.isEmpty()) {
                    entity.setRemark(currentRemark + "; " + dto.getMeasures());
                } else {
                    entity.setRemark(dto.getMeasures());
                }
                logger.info("更新处理措施: {}", dto.getMeasures());
            }
        }
        // 设置对应月份的累计新增债权数据（累加逻辑）
        setAmountByMonth(entity, newYearMonth, dto.getOverdueAmount());

        overdueDebtAddRepository.save(entity);
        return entity;
    }

    /**
     * 更新减值准备表的记录。如果记录已存在，则更新金额、备注及其他信息；否则创建新记录。
     *
     * @param dto 前端提交的 DTO 对象
     */
    public void updateImpairmentReserveTable(OverdueDebtAddDTO dto) {
        try {
            String yearMonthStr = dto.getAddDate();

            // 解析年份和月份
            String[] parts = yearMonthStr.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // 构造复合主键进行查询
            ImpairmentReserve.ImpairmentReserveKey reserveKey = new ImpairmentReserve.ImpairmentReserveKey();
            reserveKey.setCreditor(dto.getCreditor());
            reserveKey.setDebtor(dto.getDebtor());
            reserveKey.setIsLitigation(dto.getIsLitigation());
            reserveKey.setPeriod(dto.getPeriod());
            reserveKey.setYear(year);
            reserveKey.setMonth(month);


            ImpairmentReserve reserve = impairmentReserveRepository.findById(reserveKey).orElse(null);
            if (reserve == null) {
                reserve = new ImpairmentReserve();
                reserve.setId(reserveKey);
                // 初始化各金额项为 0
                // 序号字段由数据库自动生成，无需手动设置
                reserve.setCurrentMonthBalance(BigDecimal.ZERO);
                reserve.setImpairmentAmount(BigDecimal.ZERO);
                reserve.setPreviousMonthBalance(BigDecimal.ZERO);
                reserve.setCurrentMonthAmount(BigDecimal.ZERO);
                reserve.setAnnualRecoveryTarget(BigDecimal.ZERO);
                reserve.setAnnualCumulativeRecovery(BigDecimal.ZERO);
                reserve.setCurrentMonthNewDebt(BigDecimal.ZERO);
                reserve.setCurrentMonthDisposeDebt(BigDecimal.ZERO);
                logger.info("未找到现有减值准备记录，将创建新记录");
            } else {
                logger.info("找到现有减值准备记录，将更新记录: ID={}", reserve.getId());
            }

            // 设置案件名称（仅当涉诉且 DTO 中相应字段不为空时设置）
            if ("是".equals(dto.getIsLitigation())) {
                if (dto.getCaseName() != null && !dto.getCaseName().isEmpty()) {
                    reserve.setCaseName(dto.getCaseName());
                } else if (dto.getLitigationName() != null && !dto.getLitigationName().isEmpty()) {
                    reserve.setCaseName(dto.getLitigationName());
                }
            }
            reserve.setManagementCompany(dto.getManagementCompany());

            // 设置科目名称
            if (dto.getSubjectName() != null && !dto.getSubjectName().isEmpty()) {
                reserve.setSubjectName(dto.getSubjectName());
                logger.info("设置减值准备表科目名称: {}", dto.getSubjectName());
            }

            // 设置债权类型和债权性质
            if (dto.getDebtCategory() != null && !dto.getDebtCategory().isEmpty()) {
                reserve.setDebtType(dto.getDebtCategory());
                logger.info("设置减值准备表债权类型: {}", dto.getDebtCategory());
            }
            if (dto.getDebtNature() != null && !dto.getDebtNature().isEmpty()) {
                reserve.setDebtNature(dto.getDebtNature());
                logger.info("设置减值准备表债权性质: {}", dto.getDebtNature());
            }

            // 设置逾期日期
            if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                try {
                    Date dueDate = parseDueDate(dto.getOverdueDate());
                    reserve.setDueDate(dueDate);
                    logger.info("设置减值准备表到期时间: {}", dto.getOverdueDate());
                } catch (ParseException e) {
                    logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                }
            }

            // 编辑备注信息：追加处理措施
            if (dto.getMeasures() != null && !dto.getMeasures().isEmpty()) {
                if (reserve.getRemark() != null && !reserve.getRemark().isEmpty()) {
                    reserve.setRemark(reserve.getRemark() + "; " + dto.getMeasures());
                } else {
                    reserve.setRemark(dto.getMeasures());
                }
            }

            // 更新本月新增债权记录（仅用于记录统计）
            BigDecimal newDebtAmount = dto.getOverdueAmount() != null ? dto.getOverdueAmount() : BigDecimal.ZERO;
            BigDecimal currentMonthNewDebt = reserve.getCurrentMonthNewDebt() != null ? reserve.getCurrentMonthNewDebt() : BigDecimal.ZERO;
            reserve.setCurrentMonthNewDebt(currentMonthNewDebt.add(newDebtAmount));

            // 获取当前已有的本月末债权余额 - 保留原有债权余额计算逻辑
            BigDecimal currentDebtBalance = reserve.getCurrentMonthBalance() != null ? reserve.getCurrentMonthBalance() : BigDecimal.ZERO;
            // 将新增债权金额加到本月末债权余额
            BigDecimal updatedDebtBalance = currentDebtBalance.add(newDebtAmount);
            reserve.setCurrentMonthBalance(updatedDebtBalance);

            // 更新本月初债权余额(仅当它为空时)
            if (reserve.getLastMonthBalance() == null || reserve.getLastMonthBalance().compareTo(BigDecimal.ZERO) == 0) {
                reserve.setLastMonthBalance(currentDebtBalance);
            }

            // 获取本月初余额（减值准备）
            BigDecimal lastProvisionBalance = reserve.getPreviousMonthBalance() != null ?
                                              reserve.getPreviousMonthBalance() : BigDecimal.ZERO;

            // 处理坏账准备计提金额
            if (dto.getProvisionAmount() != null) {
                // 获取前端传入的计提金额（即本月增减）
                BigDecimal provisionAmount = dto.getProvisionAmount();

                // 1. 设置本月增减（即坏账准备计提金额）
                reserve.setCurrentMonthIncreaseDecrease(provisionAmount);
                logger.info("设置本月增减金额: {}", provisionAmount);

                // 2. 计算本月末减值准备余额 = 上月末减值准备余额 + 本月增减
                BigDecimal finalProvisionBalance = lastProvisionBalance.add(provisionAmount);
                reserve.setCurrentMonthAmount(finalProvisionBalance);
                logger.info("计算本月末减值准备余额: {} (上月末减值准备余额 {} + 本月增减 {})",
                            finalProvisionBalance, lastProvisionBalance, provisionAmount);

                // 3. 设置计提减值金额等于本月末余额而非本月增减
                if (reserve.getInitialImpairmentDate() == null) {
                    reserve.setInitialImpairmentDate(new Date());
                    logger.info("设置初始计提日期");
                }
                reserve.setImpairmentAmount(finalProvisionBalance);
                logger.info("设置计提减值金额等于本月末余额: {}", finalProvisionBalance);
            }

            // 获取当前系统年月
            Calendar currentCal = Calendar.getInstance();
            int currentYear = currentCal.get(Calendar.YEAR);
            int currentMonth = currentCal.get(Calendar.MONTH) + 1; // Calendar月份从0开始

            // 设置更新时间和创建时间（如果未设置）
            reserve.setUpdateTime(LocalDateTime.now());
            if (reserve.getCreateTime() == null) {
                reserve.setCreateTime(LocalDateTime.now());
            }

            impairmentReserveRepository.save(reserve);
            logger.info("减值准备表更新完成: ID={}", reserve.getId());

            // 判断是否为新增的减值准备数据(判断创建时间与更新时间是否相同)
            boolean isNewRecord = reserve.getCreateTime().equals(reserve.getUpdateTime());

            // 如果是新增的减值准备数据，则不进行连续更新
            if (isNewRecord) {
                logger.info("新增的减值准备数据，不进行连续更新");
                return;
            }

            // 使用前端传递的期间信息，如果没有则生成默认值
            String period;
            if (dto.getPeriod() != null && !dto.getPeriod().isEmpty()) {
                period = dto.getPeriod();
                logger.info("减值准备表使用前端传递的期间信息: {}", period);
            } else {
                // 生成默认的期间信息
                if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                    String overdueYear = dto.getOverdueDate().split("-")[0];
                    period = overdueYear + "年430";
                    logger.info("减值准备表使用逆期所属年月生成默认期间信息: {}", period);
                } else {
                    period = year + NEW_DEBT_SUFFIX;
                    logger.info("减值准备表使用新增年月生成默认期间信息: {}", period);
                }
            }

            // 判断新增债权的月份是否为当前月份
            if (year < currentYear || (year == currentYear && month < currentMonth)) {
                logger.info("检测到在非当前月份({}-{})新增减值准备，将更新后续月份数据", year, month);

                // 从下一个月开始，一直更新到当前月份
                Calendar nextMonthCal = Calendar.getInstance();
                nextMonthCal.set(Calendar.YEAR, year);
                nextMonthCal.set(Calendar.MONTH, month - 1); // Calendar月份从0开始
                nextMonthCal.add(Calendar.MONTH, 1);

                while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                       (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                        nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                    int nextYear = nextMonthCal.get(Calendar.YEAR);
                    int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                    // 直接使用前端传递过来的债权人、债务人、归属期间、年份和下个月的月份进行筛选
                    logger.info("开始更新后续月份减值准备数据: {}-{}, 使用期间: {}", nextYear, nextMonth, dto.getPeriod());

                    // 构造下一个月的复合主键，使用前端传递的期间
                    ImpairmentReserve.ImpairmentReserveKey nextReserveKey = new ImpairmentReserve.ImpairmentReserveKey();
                    nextReserveKey.setCreditor(dto.getCreditor());
                    nextReserveKey.setDebtor(dto.getDebtor());
                    nextReserveKey.setYear(nextYear);
                    nextReserveKey.setMonth(nextMonth);
                    nextReserveKey.setIsLitigation(dto.getIsLitigation());
                    nextReserveKey.setPeriod(period);

                    Optional<ImpairmentReserve> nextMonthRecord = impairmentReserveRepository.findById(nextReserveKey);

                    if (nextMonthRecord.isPresent()) {
                        ImpairmentReserve nextReserve = nextMonthRecord.get();

                        // 获取当前月份的本月末债权余额作为下一个月的上月末余额
                        BigDecimal prevMonthBalance = reserve.getCurrentMonthBalance() != null ? reserve.getCurrentMonthBalance() : BigDecimal.ZERO;

                        // 更新下一个月的上月末债权余额
                        nextReserve.setPreviousMonthBalance(prevMonthBalance);

                        // 获取当前月的本月末债权余额作为下一个月的本月初债权余额
                        nextReserve.setLastMonthBalance(prevMonthBalance);

                        // 更新债权余额继续沿用原有逻辑
                        prevMonthBalance = reserve.getCurrentMonthBalance() != null ? reserve.getCurrentMonthBalance() : BigDecimal.ZERO;
                        nextReserve.setCurrentMonthBalance(prevMonthBalance);

                        // 更新本月末减值准备余额
                        BigDecimal prevMonthFinalBalance = reserve.getCurrentMonthAmount() != null ? reserve.getCurrentMonthAmount() : BigDecimal.ZERO;
                        BigDecimal nextMonthIncrease = nextReserve.getCurrentMonthIncreaseDecrease() != null ? nextReserve.getCurrentMonthIncreaseDecrease() : BigDecimal.ZERO;
                        BigDecimal nextMonthBalance = prevMonthFinalBalance.add(nextMonthIncrease);
                        nextReserve.setCurrentMonthAmount(nextMonthBalance);

                        // 如果已有本月增减但没有本月末减值准备余额，则根据公式计算
                        if (nextReserve.getCurrentMonthIncreaseDecrease() != null &&
                            (nextReserve.getCurrentMonthAmount() == null ||
                             nextReserve.getCurrentMonthAmount().compareTo(BigDecimal.ZERO) == 0)) {
                            nextReserve.setCurrentMonthAmount(nextReserve.getPreviousMonthBalance().add(nextReserve.getCurrentMonthIncreaseDecrease()));
                        }

                        // 保存更新后的下一个月记录
                        impairmentReserveRepository.save(nextReserve);
                        logger.info("已更新后续月份({}-{})减值准备数据: 本月初债权余额={}, 本月末债权余额={}",
                                    nextYear, nextMonth, nextReserve.getLastMonthBalance(), nextReserve.getCurrentMonthBalance());

                        // 更新当前月份引用，用于下一次循环
                        reserve = nextReserve;
                    } else {
                        logger.info("未找到后续月份({}-{})的减值准备记录，将创建新记录", nextYear, nextMonth);

                        // 创建新的后续月份记录
                        ImpairmentReserve nextReserve = new ImpairmentReserve();
                        nextReserve.setId(nextReserveKey);

                        // 将上个月记录的基础信息复制到新记录
                        // 如果案件名称为空，则设置为"无"
                        if (reserve.getCaseName() != null && !reserve.getCaseName().isEmpty()) {
                            nextReserve.setCaseName(reserve.getCaseName());
                        } else {
                            nextReserve.setCaseName("无");
                        }
                        // 复制科目名称
                        nextReserve.setSubjectName(reserve.getSubjectName());
                        nextReserve.setManagementCompany(reserve.getManagementCompany());
                        nextReserve.setRemark(reserve.getRemark());
                        nextReserve.setSequence(reserve.getSequence());

                        // 获取当前月份的本月末余额作为下一个月的上月末余额
                        BigDecimal prevMonthBalance = reserve.getCurrentMonthBalance() != null ? reserve.getCurrentMonthBalance() : BigDecimal.ZERO;

                        // 设置上月末余额
                        nextReserve.setPreviousMonthBalance(prevMonthBalance);

                        // 设置本月初债权余额等于上月末余额
                        nextReserve.setLastMonthBalance(prevMonthBalance);

                        // 继续沿用原有债权余额的逻辑
                        nextReserve.setCurrentMonthBalance(prevMonthBalance);

                        // 设置本月末减值准备余额等于本月初减值准备余额（因为没有本月增减）
                        nextReserve.setCurrentMonthAmount(prevMonthBalance);
                        // 设置本月增减为0
                        nextReserve.setCurrentMonthIncreaseDecrease(BigDecimal.ZERO);

                        // 特殊处理：如果是2022年4月30日，余额设为0.00
                        if (nextYear == 2022 && nextMonth == 4) {
                            nextReserve.setCurrentMonthBalance(BigDecimal.ZERO);
                            nextReserve.setLastMonthBalance(BigDecimal.ZERO);
                            nextReserve.setCurrentMonthAmount(BigDecimal.ZERO);
                            nextReserve.setCurrentMonthIncreaseDecrease(BigDecimal.ZERO);
                        }

                        // 设置计提减值金额与上个月相同
                        nextReserve.setImpairmentAmount(reserve.getImpairmentAmount());
                        nextReserve.setInitialImpairmentDate(reserve.getInitialImpairmentDate());

                        // 设置本月增减=0
                        nextReserve.setCurrentMonthAmount(BigDecimal.ZERO);

                        // 设置本月新增债权=0
                        nextReserve.setCurrentMonthNewDebt(BigDecimal.ZERO);

                        // 设置本月处置债权=0
                        nextReserve.setCurrentMonthDisposeDebt(BigDecimal.ZERO);

                        // 年度回收目标和累计回收保持一致
                        nextReserve.setAnnualRecoveryTarget(reserve.getAnnualRecoveryTarget());
                        nextReserve.setAnnualCumulativeRecovery(reserve.getAnnualCumulativeRecovery());

                        // 设置创建时间和更新时间
                        LocalDateTime now = LocalDateTime.now();
                        nextReserve.setCreateTime(now);
                        nextReserve.setUpdateTime(now);

                        // 保存新创建的下一个月记录
                        impairmentReserveRepository.save(nextReserve);
                        logger.info("已创建后续月份({}-{})减值准备数据: 本月初债权余额={}, 本月末债权余额={}",
                                    nextYear, nextMonth, nextReserve.getLastMonthBalance(), nextReserve.getCurrentMonthBalance());

                        // 更新当前月份引用，用于下一次循环
                        reserve = nextReserve;
                    }

                    // 移动到下一个月
                    nextMonthCal.add(Calendar.MONTH, 1);
                }
            }
        } catch (IllegalArgumentException | NullPointerException e) {
            logger.error("更新减值准备表时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("更新减值准备表失败: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("更新减值准备表时发生其他错误", e);
            throw new RuntimeException("更新减值准备表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新非诉讼表记录。如果记录存在则更新，否则尝试基于上月数据创建新记录。
     *
     * @param dto 前端提交的 DTO 对象
     */
    public void updateNonLitigationClaimTable(OverdueDebtAddDTO dto) {
        try {
            // 新增年月（格式预期：yyyy-MM，例如 "2025-05"）
            String yearMonthStr = dto.getAddDate();
            logger.info("开始更新非诉讼表: 债权人={}, 债务人={}, 年月={}",
                        dto.getCreditor(), dto.getDebtor(), yearMonthStr);

            // 解析年月
            String[] parts = yearMonthStr.split("-");
            Integer year = Integer.valueOf(parts[0]);
            Integer month = Integer.valueOf(parts[1]);

            // 使用前端传递的期间信息，如果没有则生成默认值
            String period;
            period = dto.getPeriod();

            Optional<NonLitigationClaim> existingRecord = nonLitigationClaimRepository
                    .findByCreditorAndDebtorAndYearAndMonthAndPeriod(dto.getCreditor(), dto.getDebtor(), year, month, dto.getPeriod());

            NonLitigationClaim nonLitigationClaim;
            if (existingRecord.isPresent()) {
                nonLitigationClaim = existingRecord.get();
                logger.info("找到现有非诉讼记录，将进行更新: ID={}", nonLitigationClaim.getId());
            } else {
                // 使用 Calendar.add 方法获取上个月
                Calendar cal = Calendar.getInstance();
                cal.set(Calendar.YEAR, year);
                cal.set(Calendar.MONTH, month - 1);
                cal.add(Calendar.MONTH, -1);

                Integer prevYear = cal.get(Calendar.YEAR);
                Integer prevMonth = cal.get(Calendar.MONTH) + 1;
                String prevPeriod = prevYear + NEW_DEBT_SUFFIX;

                Optional<NonLitigationClaim> prevRecord = nonLitigationClaimRepository
                        .findByCreditorAndDebtorAndYearAndMonthAndPeriod(dto.getCreditor(), dto.getDebtor(), prevYear, prevMonth, prevPeriod);

                nonLitigationClaim = new NonLitigationClaim();

                // 创建复合主键
                NonLitigationClaim.NonLitigationCompositeKey key = new NonLitigationClaim.NonLitigationCompositeKey();
                key.setCreditor(dto.getCreditor());
                key.setDebtor(dto.getDebtor());
                key.setYear(year);
                key.setMonth(month);
                key.setPeriod(dto.getPeriod());
                nonLitigationClaim.setId(key);

                if (prevRecord.isPresent()) {
                    NonLitigationClaim prev = prevRecord.get();
                    logger.info("找到上月非诉讼记录，将基于上月数据创建新记录");
                    nonLitigationClaim.setLastMonthPrincipal(prev.getCurrentMonthPrincipal());
                    nonLitigationClaim.setLastMonthInterest(prev.getCurrentMonthInterest());
                    nonLitigationClaim.setLastMonthPenalty(prev.getCurrentMonthPenalty());
                    nonLitigationClaim.setCreditorCategory(prev.getCreditorCategory());
                    nonLitigationClaim.setSubjectName(prev.getSubjectName());
                    nonLitigationClaim.setDueDate(prev.getDueDate());
                    nonLitigationClaim.setOverdueYear(prev.getOverdueYear());
                    nonLitigationClaim.setBalance20220430(prev.getBalance20220430());
                    nonLitigationClaim.setAnnualRecoveryTarget(prev.getAnnualRecoveryTarget());
                    nonLitigationClaim.setAnnualCumulativeRecovery(prev.getAnnualCumulativeRecovery());
                } else {
                    logger.info("未找到上月非诉讼记录，将创建全新记录");
                    nonLitigationClaim.setLastMonthPrincipal(BigDecimal.ZERO);
                    nonLitigationClaim.setLastMonthInterest(BigDecimal.ZERO);
                    nonLitigationClaim.setLastMonthPenalty(BigDecimal.ZERO);
                    nonLitigationClaim.setAnnualRecoveryTarget(BigDecimal.ZERO);
                    nonLitigationClaim.setAnnualCumulativeRecovery(BigDecimal.ZERO);
                    nonLitigationClaim.setBalance20220430(BigDecimal.ZERO);
                    // 解析逾期日期（支持多种格式）
                    if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                        try {
                            Date dueDate = parseDueDate(dto.getOverdueDate());
                            nonLitigationClaim.setDueDate(dueDate);
                            logger.info("成功解析非诉讼表逾期日期: {}", dto.getOverdueDate());
                        } catch (ParseException e) {
                            logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                        }
                    }
                }
            }

            // 设置管理公司和责任人等其他字段
            nonLitigationClaim.setManagementCompany(dto.getManagementCompany());
            nonLitigationClaim.setResponsiblePerson(dto.getResponsiblePerson());

            // 设置科目名称
            if (dto.getSubjectName() != null && !dto.getSubjectName().isEmpty()) {
                nonLitigationClaim.setSubjectName(dto.getSubjectName());
                logger.info("设置非诉讼表科目名称: {}", dto.getSubjectName());
            }

            // 设置债权性质
            if (dto.getDebtNature() != null && !dto.getDebtNature().isEmpty()) {
                nonLitigationClaim.setCreditorNature(dto.getDebtNature());
                logger.info("设置非诉讼表债权性质: {}", dto.getDebtNature());
            }

            // 设置债权类型
            if (dto.getDebtCategory() != null && !dto.getDebtCategory().isEmpty()) {
                nonLitigationClaim.setCreditorCategory(dto.getDebtCategory()); // 保留原有字段
                nonLitigationClaim.setDebtType(dto.getDebtCategory()); // 新增字段
                logger.info("设置非诉讼表债权类型: {}", dto.getDebtCategory());
            }

            // 设置逾期日期
            if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                try {
                    Date dueDate = parseDueDate(dto.getOverdueDate());
                    nonLitigationClaim.setDueDate(dueDate);
                    logger.info("设置非诉讼表到期时间: {}", dto.getOverdueDate());
                } catch (ParseException e) {
                    logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                }
            }

            // 先设置利息、违约金相关的默认值
            nonLitigationClaim.setCurrentMonthInterestIncreaseDecrease(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPenaltyIncreaseDecrease(BigDecimal.ZERO);

            // 获取前端传来的金额
            BigDecimal currentIncrease = dto.getOverdueAmount() != null ? dto.getOverdueAmount() : BigDecimal.ZERO;

            // 计算本月末本金
            BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal() != null ? nonLitigationClaim.getLastMonthPrincipal() : BigDecimal.ZERO;

            // 区分处理现有记录和新记录
            if (existingRecord.isPresent()) {
                // 获取现有记录中的值
                BigDecimal existingIncrease = nonLitigationClaim.getCurrentMonthPrincipalIncreaseDecrease() != null
                                              ? nonLitigationClaim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal existingNewDebt = nonLitigationClaim.getCurrentMonthNewDebt() != null
                                             ? nonLitigationClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;

                // 进行更新
                nonLitigationClaim.setCurrentMonthPrincipalIncreaseDecrease(existingIncrease.add(currentIncrease));
                nonLitigationClaim.setCurrentMonthNewDebt(existingNewDebt.add(currentIncrease));

                // 计算本月末本金：上月本金 + 之前已有的增加值 + 新增值
                BigDecimal updatedMonthPrincipal = lastMonthPrincipal.add(existingIncrease).add(currentIncrease);
                nonLitigationClaim.setCurrentMonthPrincipal(updatedMonthPrincipal);
            } else {
                // 新记录，直接设置值
                nonLitigationClaim.setCurrentMonthPrincipalIncreaseDecrease(currentIncrease);
                nonLitigationClaim.setCurrentMonthNewDebt(currentIncrease);
                BigDecimal currentMonthPrincipal = lastMonthPrincipal.add(currentIncrease);
                nonLitigationClaim.setCurrentMonthPrincipal(currentMonthPrincipal);
            }

            nonLitigationClaim.setArrangement(dto.getMeasures());
            nonLitigationClaim.setNextMonthRecoveryEstimate(BigDecimal.ZERO);

            nonLitigationClaimRepository.save(nonLitigationClaim);
            logger.info("非诉讼表更新完成: ID={}", nonLitigationClaim.getId());

            // 获取当前系统年月
            Calendar currentCal = Calendar.getInstance();
            int currentYear = currentCal.get(Calendar.YEAR);
            int currentMonth = currentCal.get(Calendar.MONTH) + 1;

            // 判断是否为新增的债权数据
            boolean isNewRecord = !existingRecord.isPresent();

            // 新增的债权数据也需要更新后续月份
            if (isNewRecord) {
                logger.info("新增的非诉讼债权数据，也将执行后续月份更新");
                // 不再提前返回，继续执行后续代码
            }

            // 直接使用前端传递的期间信息
            logger.info("使用前端传递的期间信息: {}", dto.getPeriod());

            // 判断新增债权的月份是否为当前月份
            if (year < currentYear || (year.equals(currentYear) && month < currentMonth)) {
                logger.info("检测到在非当前月份({}-{})新增非诉讼债权，将更新后续月份数据", year, month);

                // 从下一个月开始，一直更新到当前月份
                Calendar nextMonthCal = Calendar.getInstance();
                nextMonthCal.set(Calendar.YEAR, year);
                nextMonthCal.set(Calendar.MONTH, month - 1);
                nextMonthCal.add(Calendar.MONTH, 1);

                while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                       (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                        nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                    int nextYear = nextMonthCal.get(Calendar.YEAR);
                    int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                    // 直接使用前端传递过来的债权人、债务人、归属期间、年份和下个月的月份进行筛选
//                    String period = dto.getPeriod(); // 使用前端传递的期间
                    logger.info("开始更新后续月份非诉讼数据: {}-{}, 使用期间: {}", nextYear, nextMonth, period);

                    // 查找下一个月的记录，使用前端传递的期间
                    Optional<NonLitigationClaim> nextMonthRecord = nonLitigationClaimRepository
                            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                                    dto.getCreditor(), dto.getDebtor(), nextYear, nextMonth, dto.getPeriod());

                    if (nextMonthRecord.isPresent()) {
                        NonLitigationClaim nextClaim = nextMonthRecord.get();

                        // 获取当前月份的本月末本金、利息和罚息作为下一个月的上月末数据
                        BigDecimal currentMonthPrincipal = nonLitigationClaim.getCurrentMonthPrincipal() != null ?
                                                           nonLitigationClaim.getCurrentMonthPrincipal() : BigDecimal.ZERO;
                        BigDecimal currentMonthInterest = nonLitigationClaim.getCurrentMonthInterest() != null ?
                                                          nonLitigationClaim.getCurrentMonthInterest() : BigDecimal.ZERO;
                        BigDecimal currentMonthPenalty = nonLitigationClaim.getCurrentMonthPenalty() != null ?
                                                         nonLitigationClaim.getCurrentMonthPenalty() : BigDecimal.ZERO;

                        // 更新下一个月的上月末数据
                        nextClaim.setLastMonthPrincipal(currentMonthPrincipal);
                        nextClaim.setLastMonthInterest(currentMonthInterest);
                        nextClaim.setLastMonthPenalty(currentMonthPenalty);

                        // 重新计算下一个月的本月末本金
                        BigDecimal nextMonthPrincipalIncrease = nextClaim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                                                nextClaim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                        BigDecimal nextMonthPrincipal = currentMonthPrincipal.add(nextMonthPrincipalIncrease);
                        nextClaim.setCurrentMonthPrincipal(nextMonthPrincipal);

                        // 重新计算下一个月的本月末利息
                        BigDecimal nextMonthInterestIncrease = nextClaim.getCurrentMonthInterestIncreaseDecrease() != null ?
                                                               nextClaim.getCurrentMonthInterestIncreaseDecrease() : BigDecimal.ZERO;
                        BigDecimal nextMonthInterest = currentMonthInterest.add(nextMonthInterestIncrease);
                        nextClaim.setCurrentMonthInterest(nextMonthInterest);

                        // 重新计算下一个月的本月末罚息
                        BigDecimal nextMonthPenaltyIncrease = nextClaim.getCurrentMonthPenaltyIncreaseDecrease() != null ?
                                                              nextClaim.getCurrentMonthPenaltyIncreaseDecrease() : BigDecimal.ZERO;
                        BigDecimal nextMonthPenalty = currentMonthPenalty.add(nextMonthPenaltyIncrease);
                        nextClaim.setCurrentMonthPenalty(nextMonthPenalty);

                        // 保存更新后的下一个月记录
                        nonLitigationClaimRepository.save(nextClaim);
                        logger.info("已更新后续月份({}-{})非诉讼数据: 上月末本金={}, 利息={}, 罚息={}",
                                    nextYear, nextMonth, currentMonthPrincipal, currentMonthInterest, currentMonthPenalty);

                        // 更新当前月份引用，用于下一次循环
                        nonLitigationClaim = nextClaim;
                    } else {
                        logger.info("未找到后续月份({}-{})的非诉讼记录，将创建新记录", nextYear, nextMonth);

                        // 创建新的非诉讼记录
                        NonLitigationClaim nextClaim = new NonLitigationClaim();

                        // 创建主键
                        NonLitigationClaim.NonLitigationCompositeKey nextKey = new NonLitigationClaim.NonLitigationCompositeKey();
                        nextKey.setCreditor(nonLitigationClaim.getId().getCreditor());
                        nextKey.setDebtor(nonLitigationClaim.getId().getDebtor());
                        nextKey.setPeriod(nonLitigationClaim.getId().getPeriod());
                        nextKey.setYear(nextYear);
                        nextKey.setMonth(nextMonth);
                        nextClaim.setId(nextKey);

                        // 复制基础信息
                        nextClaim.setManagementCompany(nonLitigationClaim.getManagementCompany());
                        nextClaim.setResponsiblePerson(nonLitigationClaim.getResponsiblePerson());
                        nextClaim.setCreditorCategory(nonLitigationClaim.getCreditorCategory());
                        nextClaim.setCreditorNature(nonLitigationClaim.getCreditorNature());
                        nextClaim.setSubjectName(nonLitigationClaim.getSubjectName());
                        nextClaim.setDueDate(nonLitigationClaim.getDueDate());
                        nextClaim.setCurrentMonthPenaltyIncreaseDecrease(BigDecimal.ZERO);
                        nextClaim.setCurrentMonthNewDebt(BigDecimal.ZERO);

                        // 获取当前月份的本月末本金、利息和罚息
                        BigDecimal currentMonthPrincipal = nonLitigationClaim.getCurrentMonthPrincipal() != null ?
                                                           nonLitigationClaim.getCurrentMonthPrincipal() : BigDecimal.ZERO;
                        BigDecimal currentMonthInterest = nonLitigationClaim.getCurrentMonthInterest() != null ?
                                                          nonLitigationClaim.getCurrentMonthInterest() : BigDecimal.ZERO;
                        BigDecimal currentMonthPenalty = nonLitigationClaim.getCurrentMonthPenalty() != null ?
                                                         nonLitigationClaim.getCurrentMonthPenalty() : BigDecimal.ZERO;

                        // 设置上月末数据
                        nextClaim.setLastMonthPrincipal(currentMonthPrincipal);
                        nextClaim.setLastMonthInterest(currentMonthInterest);
                        nextClaim.setLastMonthPenalty(currentMonthPenalty);

                        // 设置新记录的本月末数据等于上月末数据
                        nextClaim.setCurrentMonthPrincipal(currentMonthPrincipal);
                        nextClaim.setCurrentMonthInterest(currentMonthInterest);
                        nextClaim.setCurrentMonthPenalty(currentMonthPenalty);

                        // 设置新记录的本月变动为0
                        nextClaim.setCurrentMonthPrincipalIncreaseDecrease(BigDecimal.ZERO);
                        nextClaim.setCurrentMonthInterestIncreaseDecrease(BigDecimal.ZERO);

                        // 保存新记录
                        nonLitigationClaimRepository.save(nextClaim);
                        logger.info("创建了后续月份({}-{})非诉讼数据: 上月末本金={}, 利息={}, 罚息={}",
                                    nextYear, nextMonth, currentMonthPrincipal, currentMonthInterest, currentMonthPenalty);

                        // 更新当前记录引用，用于下一次循环
                        nonLitigationClaim = nextClaim;
                    }

                    // 移动到下一个月
                    nextMonthCal.add(Calendar.MONTH, 1);
                }
            }
        } catch (IllegalArgumentException | NullPointerException e) {
            logger.error("更新非诉证表时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("更新非诉证表失败: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("更新非诉证表时发生其他错误", e);
            throw new RuntimeException("更新非诉证表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新诉讼表记录。如果记录存在则更新，否则尝试基于上月数据创建新记录。
     *
     * @param dto 前端提交的 DTO 对象
     */
    public void updateLitigationClaimTable(OverdueDebtAddDTO dto) {
        try {
            String yearMonthStr = dto.getAddDate();
            // 解析年月信息
            String[] parts = yearMonthStr.split("-");
            Integer year = Integer.valueOf(parts[0]);
            Integer month = Integer.valueOf(parts[1]);

            // 选择正确的期间信息
            String period;

            // 如果有选择逆期所属年月，使用逆期所属年月的年份
            if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                String overdueYear = dto.getOverdueDate().split("-")[0];
                period = overdueYear + "年430";
                logger.info("诉讼表使用逆期所属年月生成期间信息: {}", period);
            } else {
                // 按照年获取期间标识，例如："2025年新增债权"
                period = year + NEW_DEBT_SUFFIX;
                logger.info("诉讼表使用新增年月生成期间信息: {}", period);
            }

            Optional<LitigationClaim> existingRecord = litigationClaimRepository
                    .findByCreditorAndDebtorAndYearAndMonthAndPeriod(dto.getCreditor(), dto.getDebtor(), year, month, dto.getPeriod());

            LitigationClaim litigationClaim;
            if (existingRecord.isPresent()) {
                litigationClaim = existingRecord.get();
                logger.info("找到现有诉讼记录，将进行更新: ID={}", litigationClaim.getId());

                // 更新诉讼记录的债权类别
                if (dto.getDebtNature() != null && !dto.getDebtNature().isEmpty()) {
                    litigationClaim.setClaimType(dto.getDebtNature());
                    logger.info("更新诉讼表债权类别: {}", dto.getDebtNature());
                }

                // 设置科目名称到备注字段
                if (dto.getSubjectName() != null && !dto.getSubjectName().isEmpty()) {
                    String currentRemark = litigationClaim.getRemark();
                    if (currentRemark != null && !currentRemark.isEmpty()) {
                        litigationClaim.setRemark(currentRemark + "; 科目名称: " + dto.getSubjectName());
                    } else {
                        litigationClaim.setRemark("科目名称: " + dto.getSubjectName());
                    }
                    logger.info("将科目名称添加到备注中: {}", dto.getSubjectName());
                }

                // 更新诉讼记录的逾期日期
                if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                    try {
                        Date dueDate = parseDueDate(dto.getOverdueDate());
                        litigationClaim.setDueDate(dueDate);
                        // 同时设置逾期年份(转为字符串)
                        Calendar calYear = Calendar.getInstance();
                        calYear.setTime(dueDate);
                        litigationClaim.setOverdueYear(String.valueOf(calYear.get(Calendar.YEAR)));
                        logger.info("更新诉讼表逾期日期: {}, 逾期年份: {}", dto.getOverdueDate(), calYear.get(Calendar.YEAR));
                    } catch (ParseException e) {
                        logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                    }
                }
            } else {
                // 使用 Calendar.add 方法获取上个月
                Calendar cal = Calendar.getInstance();
                cal.set(Calendar.YEAR, year);
                cal.set(Calendar.MONTH, month - 1);
                cal.add(Calendar.MONTH, -1);

                Integer prevYear = cal.get(Calendar.YEAR);
                Integer prevMonth = cal.get(Calendar.MONTH) + 1;
                String prevPeriod = prevYear + NEW_DEBT_SUFFIX;

                Optional<LitigationClaim> prevRecord = litigationClaimRepository
                        .findByCreditorAndDebtorAndYearAndMonthAndPeriod(dto.getCreditor(), dto.getDebtor(), prevYear, prevMonth, prevPeriod);

                litigationClaim = new LitigationClaim();
                if (prevRecord.isPresent()) {
                    LitigationClaim prev = prevRecord.get();
                    logger.info("找到上月诉讼记录，将基于上月数据创建新记录");
                    litigationClaim.setLastMonthDebtBalance(prev.getCurrentMonthDebtBalance());
                    litigationClaim.setLitigationCase(prev.getLitigationCase());
                    litigationClaim.setClaimType(prev.getClaimType());
                    litigationClaim.setDueDate(prev.getDueDate());
                    litigationClaim.setOverdueYear(prev.getOverdueYear());
                    litigationClaim.setAnnualRecoveryTarget(prev.getAnnualRecoveryTarget());
                    litigationClaim.setAnnualCumulativeRecovery(prev.getAnnualCumulativeRecovery());
                    litigationClaim.setSequence(prev.getSequence());
                } else {
                    logger.info("未找到上月诉讼记录，将创建全新记录");
                    litigationClaim.setLastMonthDebtBalance(BigDecimal.ZERO);
                    litigationClaim.setAnnualRecoveryTarget("0");
                    litigationClaim.setAnnualCumulativeRecovery(BigDecimal.ZERO);
                    // 设置默认序号，数据库自增覆盖此值
                    litigationClaim.setSequence(0);
                    if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                        try {
                            Date dueDate = parseDueDate(dto.getOverdueDate());
                            litigationClaim.setDueDate(dueDate);
                            // 同时设置逾期年份(转为字符串)
                            Calendar calYear = Calendar.getInstance();
                            calYear.setTime(dueDate);
                            litigationClaim.setOverdueYear(String.valueOf(calYear.get(Calendar.YEAR)));
                            logger.info("成功解析诉讼表逾期日期: {}, 设置逾期年份: {}", dto.getOverdueDate(), calYear.get(Calendar.YEAR));
                        } catch (ParseException e) {
                            logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                        }
                    }
                }
                // 设置诉讼表的复合主键
                LitigationCompositeKey compositeKey = new LitigationCompositeKey();
                compositeKey.setCreditor(dto.getCreditor());
                compositeKey.setDebtor(dto.getDebtor());
                compositeKey.setPeriod(dto.getPeriod()); // 使用前端传递的期间信息
                compositeKey.setYear(year);
                compositeKey.setMonth(month);
                litigationClaim.setId(compositeKey);
            }

            // 如果还没有设置复合主键，则设置一个
            if (litigationClaim.getId() == null) {
                LitigationCompositeKey key = new LitigationCompositeKey();
                key.setCreditor(dto.getCreditor());
                key.setDebtor(dto.getDebtor());
                key.setYear(year);
                key.setMonth(month);
                key.setPeriod(dto.getPeriod()); // 使用前端传递的期间信息
                litigationClaim.setId(key);
            }

            // 设置诉讼案件名称
            if (dto.getCaseName() != null && !dto.getCaseName().isEmpty()) {
                litigationClaim.setLitigationCase(dto.getCaseName());
            } else if (dto.getLitigationName() != null && !dto.getLitigationName().isEmpty()) {
                litigationClaim.setLitigationCase(dto.getLitigationName());
            } else if (litigationClaim.getLitigationCase() == null || litigationClaim.getLitigationCase().isEmpty()) {
                litigationClaim.setLitigationCase(dto.getCreditor() + "-" + dto.getDebtor() + "-诉讼");
            }

            // 设置管理公司和责任人
            litigationClaim.setManagementCompany(dto.getManagementCompany());
            litigationClaim.setResponsiblePerson(dto.getResponsiblePerson());

            // 设置科目名称、债权类型和债权性质
            if (dto.getSubjectName() != null && !dto.getSubjectName().isEmpty()) {
                litigationClaim.setSubjectName(dto.getSubjectName());
                logger.info("设置诉讼表科目名称: {}", dto.getSubjectName());
            }
            if (dto.getDebtCategory() != null && !dto.getDebtCategory().isEmpty()) {
                litigationClaim.setDebtType(dto.getDebtCategory());
                logger.info("设置诉讼表债权类型: {}", dto.getDebtCategory());
            }
            if (dto.getDebtNature() != null && !dto.getDebtNature().isEmpty()) {
                litigationClaim.setDebtNature(dto.getDebtNature());
                litigationClaim.setClaimType(dto.getDebtNature());
                logger.info("设置诉讼表债权性质: {}", dto.getDebtNature());
            }

            // 设置逾期日期
            if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                try {
                    Date dueDate = parseDueDate(dto.getOverdueDate());
                    litigationClaim.setDueDate(dueDate);

                    // 同时设置逾期年份(转为字符串)
                    Calendar calYear = Calendar.getInstance();
                    calYear.setTime(dueDate);
                    litigationClaim.setOverdueYear(String.valueOf(calYear.get(Calendar.YEAR)));

                    logger.info("设置诉讼表到期时间: {}, 逾期年份: {}", dto.getOverdueDate(), calYear.get(Calendar.YEAR));
                } catch (ParseException e) {
                    logger.error("解析逾期日期失败: {}", dto.getOverdueDate(), e);
                }
            }

            // 更新本月新增债权和诉讼本金（累加逻辑）
            BigDecimal currentIncrease = dto.getOverdueAmount() != null ? dto.getOverdueAmount() : BigDecimal.ZERO;
            if (existingRecord.isPresent()) {
                BigDecimal existingNewDebt = litigationClaim.getCurrentMonthNewDebt() != null
                                             ? litigationClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                litigationClaim.setCurrentMonthNewDebt(existingNewDebt.add(currentIncrease));
                BigDecimal existingPrincipal = litigationClaim.getLitigationPrincipal() != null
                                               ? litigationClaim.getLitigationPrincipal() : BigDecimal.ZERO;
                litigationClaim.setLitigationPrincipal(existingPrincipal.add(currentIncrease));
            } else {
                litigationClaim.setCurrentMonthNewDebt(currentIncrease);
                litigationClaim.setLitigationPrincipal(currentIncrease);
                litigationClaim.setLitigationInterest(BigDecimal.ZERO);
            }

            // 设置诉讼主张相关金额（仅当对应字段为空时设置）
            LitigationClaimDTO litigationClaimDTO = dto.getLitigationClaimDTO();
            if (existingRecord.isEmpty() || litigationClaim.getLitigationOccurredPrincipal() == null) {
                litigationClaim.setLitigationOccurredPrincipal(BigDecimal.valueOf(litigationClaimDTO.getPrincipal()));
                litigationClaim.setLitigationInterestFee(BigDecimal.valueOf(litigationClaimDTO.getInterest()));
                litigationClaim.setLitigationFee(BigDecimal.valueOf(litigationClaimDTO.getLitigationFee()));
                litigationClaim.setIntermediaryFee(BigDecimal.valueOf(litigationClaimDTO.getIntermediaryFee()));
            }

            // 计算本月末债权余额 = 上月末债权余额 + 本月新增债权 - 本月处置债权
            BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance() != null ? litigationClaim.getLastMonthDebtBalance() : BigDecimal.ZERO;
            BigDecimal currentMonthDisposal = litigationClaim.getCurrentMonthDisposalDebt() != null ? litigationClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
            BigDecimal currentMonthNewDebt = litigationClaim.getCurrentMonthNewDebt() != null ? litigationClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
            BigDecimal currentMonthBalance = lastMonthBalance.add(currentMonthNewDebt).subtract(currentMonthDisposal);
            litigationClaim.setCurrentMonthDebtBalance(currentMonthBalance);

            // 计算涉诉应收利息
            BigDecimal litigationPrincipal = litigationClaim.getLitigationPrincipal() != null ? litigationClaim.getLitigationPrincipal() : BigDecimal.ZERO;
            BigDecimal litigationInterest = currentMonthBalance.subtract(litigationPrincipal);
            litigationClaim.setLitigationInterest(litigationInterest);
            logger.info("更新涉诉债权信息: 涉诉债权本金={}, 涉诉债权应收利息={}, 本月末债权余额={}",
                        litigationPrincipal, litigationInterest, currentMonthBalance);

            // 设置诉讼表特定字段，如果前端没有传值则默认设置为0.00
            // 终审判决仲裁裁决调解和解金额
            if (litigationClaim.getFinalJudgmentAmount() == null) {
                litigationClaim.setFinalJudgmentAmount(BigDecimal.ZERO);
                logger.info("终审判决仲裁裁决调解和解金额未提供，设置默认值为0.00");
            }

            // 申请执行金额
            if (litigationClaim.getExecutionAmount() == null) {
                litigationClaim.setExecutionAmount(BigDecimal.ZERO);
                logger.info("申请执行金额未提供，设置默认值为0.00");
            }

            // 实际执行回款
            if (litigationClaim.getActualExecutionAmount() == null) {
                litigationClaim.setActualExecutionAmount(BigDecimal.ZERO);
                logger.info("实际执行回款未提供，设置默认值为0.00");
            }

            // 实际支付费用
            if (litigationClaim.getActualPaymentFee() == null) {
                litigationClaim.setActualPaymentFee(BigDecimal.ZERO);
                logger.info("实际支付费用未提供，设置默认值为0.00");
            }

            litigationClaim.setArrangement(dto.getMeasures());
            litigationClaimRepository.save(litigationClaim);
            logger.info("诉讼表更新完成: ID={}", litigationClaim.getId());

            // 获取当前系统年月
            Calendar currentCal = Calendar.getInstance();
            int currentYear = currentCal.get(Calendar.YEAR);
            int currentMonth = currentCal.get(Calendar.MONTH) + 1; // Calendar月份从0开始

            // 判断是否为新增的债权数据
            boolean isNewRecord = !existingRecord.isPresent();

            // 新增的债权数据也需要更新后续月份
            if (isNewRecord) {
                logger.info("新增的非诉讼债权数据，也将执行后续月份更新");
                // 不再提前返回，继续执行后续代码
            }

            // 使用前端传递的期间信息，如果没有则生成默认值
//            String period;
            if (dto.getPeriod() != null && !dto.getPeriod().isEmpty()) {
//                period = dto.getPeriod();
                logger.info("诉讼表使用前端传递的期间信息: {}", dto.getPeriod());
            } else {
                // 生成默认的期间信息
                if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
                    String overdueYear = dto.getOverdueDate().split("-")[0];
                    period = overdueYear + "年430";
                    logger.info("诉讼表使用逆期所属年月生成默认期间信息: {}", period);
                } else {
                    period = year + NEW_DEBT_SUFFIX;
                    logger.info("诉讼表使用新增年月生成默认期间信息: {}", period);
                }
            }

            // 更新其他月份期初和期末余额
            if (year < currentYear || (year.equals(currentYear) && month < currentMonth)) {
                logger.info("检测到在非当前月份({}-{})新增债权，将更新后续月份数据", year, month);

                // 从下一个月开始，一直更新到当前月份
                Calendar nextMonthCal = Calendar.getInstance();
                nextMonthCal.set(Calendar.YEAR, year);
                nextMonthCal.set(Calendar.MONTH, month - 1); // Calendar月份从0开始
                nextMonthCal.add(Calendar.MONTH, 1);

                while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                       (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                        nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                    int nextYear = nextMonthCal.get(Calendar.YEAR);
                    int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                    // 直接使用前端传递过来的债权人、债务人、归属期间、年份和下个月的月份进行筛选
//                    String period = dto.getPeriod(); // 使用前端传递的期间
                    logger.info("开始更新后续月份数据: {}-{}, 使用期间: {}", nextYear, nextMonth, dto.getPeriod());

                    // 查找下一个月的记录，使用前端传递的期间
                    Optional<LitigationClaim> nextMonthRecord = litigationClaimRepository
                            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                                    dto.getCreditor(), dto.getDebtor(), nextYear, nextMonth, dto.getPeriod());

                    if (nextMonthRecord.isPresent()) {
                        LitigationClaim nextClaim = nextMonthRecord.get();

                        // 获取当前月份的本月末债权余额作为下一个月的上月末债权余额
                        BigDecimal currentMonthEndBalance = litigationClaim.getCurrentMonthDebtBalance();

                        // 更新下一个月的上月末债权余额
                        nextClaim.setLastMonthDebtBalance(currentMonthEndBalance);

                        // 重新计算下一个月的本月末债权余额
                        BigDecimal nextMonthNewDebt = nextClaim.getCurrentMonthNewDebt() != null ?
                                                      nextClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                        BigDecimal nextMonthDisposal = nextClaim.getCurrentMonthDisposalDebt() != null ?
                                                       nextClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
                        BigDecimal nextMonthBalance = currentMonthEndBalance.add(nextMonthNewDebt).subtract(nextMonthDisposal);
                        nextClaim.setCurrentMonthDebtBalance(nextMonthBalance);

                        // 重新计算涉诉应收利息
                        BigDecimal nextLitigationPrincipal = nextClaim.getLitigationPrincipal() != null ?
                                                             nextClaim.getLitigationPrincipal() : BigDecimal.ZERO;
                        BigDecimal nextLitigationInterest = nextMonthBalance.subtract(nextLitigationPrincipal);
                        nextClaim.setLitigationInterest(nextLitigationInterest);

                        // 保存更新后的下一个月记录
                        litigationClaimRepository.save(nextClaim);
                        logger.info("已更新后续月份({}-{})数据: 上月末债权余额={}, 本月末债权余额={}",
                                    nextYear, nextMonth, currentMonthEndBalance, nextMonthBalance);

                        // 更新当前月份引用，用于下一次循环
                        litigationClaim = nextClaim;
                    } else {
                        logger.info("未找到后续月份({}-{})的诉讼记录，将创建新记录", nextYear, nextMonth);

                        // 创建新的诉讼记录
                        LitigationClaim nextClaim = new LitigationClaim();

                        // 创建主键
                        LitigationClaim.LitigationCompositeKey nextKey = new LitigationClaim.LitigationCompositeKey();
                        nextKey.setCreditor(litigationClaim.getId().getCreditor());
                        nextKey.setDebtor(litigationClaim.getId().getDebtor());
                        nextKey.setPeriod(litigationClaim.getId().getPeriod());
                        nextKey.setYear(nextYear);
                        nextKey.setMonth(nextMonth);
                        nextClaim.setId(nextKey);

                        // 复制基础信息
                        nextClaim.setManagementCompany(litigationClaim.getManagementCompany());
                        nextClaim.setLitigationCase(litigationClaim.getLitigationCase());
                        nextClaim.setClaimType(litigationClaim.getClaimType());
                        nextClaim.setDueDate(litigationClaim.getDueDate());
                        nextClaim.setOverdueYear(litigationClaim.getOverdueYear());
                        nextClaim.setSequence(litigationClaim.getSequence());
                        nextClaim.setAnnualRecoveryTarget(litigationClaim.getAnnualRecoveryTarget());
                        nextClaim.setAnnualCumulativeRecovery(litigationClaim.getAnnualCumulativeRecovery());

                        // 获取当前月份的本月末债权余额作为下一个月的上月末债权余额
                        BigDecimal currentMonthEndBalance = litigationClaim.getCurrentMonthDebtBalance() != null ?
                                                            litigationClaim.getCurrentMonthDebtBalance() : BigDecimal.ZERO;

                        // 设置上月末债权余额
                        nextClaim.setLastMonthDebtBalance(currentMonthEndBalance);

                        // 设置本月新增债权和处置债权为0
                        nextClaim.setCurrentMonthNewDebt(BigDecimal.ZERO);
                        nextClaim.setCurrentMonthDisposalDebt(BigDecimal.ZERO);

                        // 设置本月末债权余额等于上月末债权余额
                        nextClaim.setCurrentMonthDebtBalance(currentMonthEndBalance);

                        // 复制涉诉本金和利息信息
                        nextClaim.setLitigationPrincipal(litigationClaim.getLitigationPrincipal());
                        nextClaim.setLitigationInterest(litigationClaim.getLitigationInterest());

                        // 复制判决金额和执行金额
                        nextClaim.setFinalJudgmentAmount(litigationClaim.getFinalJudgmentAmount());
                        nextClaim.setExecutionAmount(litigationClaim.getExecutionAmount());
                        nextClaim.setActualExecutionAmount(litigationClaim.getActualExecutionAmount());
                        nextClaim.setActualPaymentFee(litigationClaim.getActualPaymentFee());

                        // 设置处理措施
                        nextClaim.setArrangement(litigationClaim.getArrangement());

                        // 保存新记录
                        litigationClaimRepository.save(nextClaim);
                        logger.info("创建了后续月份({}-{})诉讼数据: 上月末债权余额={}, 本月末债权余额={}",
                                    nextYear, nextMonth, currentMonthEndBalance, currentMonthEndBalance);

                        // 更新当前记录引用，用于下一次循环
                        litigationClaim = nextClaim;
                    }

                    // 移动到下一个月
                    nextMonthCal.add(Calendar.MONTH, 1);
                }
            }
        } catch (Exception e) {
            logger.error("更新诉讼表时发生错误", e);
            throw new RuntimeException("更新诉讼表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据债权人和债务人查询新增表中的记录，用于前端搜索功能。
     * 对于相同债权人、债务人、期间和是否涉诉的记录，只保留最新的数据（根据年份和月份判断）。
     *
     * @param creditor 债权人
     * @param debtor   债务人
     * @return 含有匹配信息及金额详情的记录集合
     */
    public List<Map<String, Object>> findDebtRecordsByCreditorAndDebtor(String creditor, String debtor) {
        List<ImpairmentReserve> records = impairmentReserveRepository.findByIdCreditorAndIdDebtor(creditor, debtor);

        // 使用Map存储最新记录，键为"债权人-债务人-期间-是否涉诉"组合
        Map<String, ImpairmentReserve> latestRecordsMap = new HashMap<>();

        // 遍历所有记录，保留最新的记录
        for (ImpairmentReserve record : records) {
            ImpairmentReserve.ImpairmentReserveKey key = record.getId();
            String mapKey = key.getCreditor() + "-" + key.getDebtor() + "-" + key.getPeriod() + "-" + key.getIsLitigation();

            // 如果Map中不存在该键或者当前记录比Map中的记录更新，则更新Map
            if (!latestRecordsMap.containsKey(mapKey) || isRecordNewer(record, latestRecordsMap.get(mapKey))) {
                latestRecordsMap.put(mapKey, record);
            }
        }

        // 将最新记录转换为结果列表
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (ImpairmentReserve latestRecord : latestRecordsMap.values()) {
            Map<String, Object> recordMap = new HashMap<>();
            ImpairmentReserve.ImpairmentReserveKey key = latestRecord.getId();

            // 从主键中取字段
            recordMap.put("creditor", key.getCreditor());
            recordMap.put("debtor", key.getDebtor());
            recordMap.put("period", key.getPeriod());
            recordMap.put("isLitigation", key.getIsLitigation());

            // 从主对象中取字段
            recordMap.put("managementCompany", latestRecord.getManagementCompany());
            recordMap.put("newAmount", latestRecord.getCurrentMonthNewDebt());
            recordMap.put("reductionAmount", latestRecord.getCurrentMonthDisposeDebt());
            recordMap.put("debtBalance", latestRecord.getCurrentMonthBalance());

            // 添加到结果集
            resultList.add(recordMap);
        }
        return resultList;
    }

    public List<java.util.Map<String, Object>> findAddDebtorInfoByYear(int year) {
        List<ImpairmentReserve> records = impairmentReserveRepository.findAddByYear(year);
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (ImpairmentReserve record : records) {
            Map<String, Object> recordMap = new java.util.HashMap<>();

            recordMap.put("creditor", record.getId().getCreditor());
            recordMap.put("debtor", record.getId().getDebtor());
            recordMap.put("managementCompany", record.getManagementCompany());
            recordMap.put("month", record.getId().getMonth());
            recordMap.put("isLitigation", record.getId().getIsLitigation());
            recordMap.put("year", record.getId().getYear());
            recordMap.put("currentMonthNewDebt", record.getCurrentMonthNewDebt());
            recordMap.put("period", record.getId().getPeriod());

            resultList.add(recordMap);
        }
        return resultList;
    }

    /**
     * 判断记录是否更新（通过比较年份和月份）
     *
     * @param newRecord      新记录
     * @param existingRecord 已存在的记录
     * @return 如果新记录比已存在的记录更新则返回true
     */
    private boolean isRecordNewer(ImpairmentReserve newRecord, ImpairmentReserve existingRecord) {
        ImpairmentReserve.ImpairmentReserveKey newKey = newRecord.getId();
        ImpairmentReserve.ImpairmentReserveKey existingKey = existingRecord.getId();

        // 首先比较年份
        if (newKey.getYear() > existingKey.getYear()) {
            return true;
        } else if (newKey.getYear() < existingKey.getYear()) {
            return false;
        }

        // 年份相同时比较月份
        return newKey.getMonth() > existingKey.getMonth();
    }

    /**
     * 根据新增年月设置对应月份的债权金额。
     * 若该月份已有记录，则累加传入金额，否则直接设置该金额。
     *
     * @param entity        OverdueDebtAdd 实体对象
     * @param newYearMonth  新增年月字符串（格式：yyyy-MM 或 yyyy年MM）
     * @param overdueAmount 本次新增的逾期金额
     */
    private void setAmountByMonth(OverdueDebtAdd entity, String newYearMonth, BigDecimal overdueAmount) {
        // 解析年月，去除可能的中文字符
        String month = newYearMonth.split("-")[1].replace("月", "").trim();
        String year = newYearMonth.split("-")[0].replace("年", "").trim();
        entity.setYear(year);
        if (month.length() == 1) {
            month = "0" + month;
        }
        // 根据月份累加对应金额
        switch (month) {
            case "01" -> {
                BigDecimal currentAmount = entity.getAmountJan() != null ? entity.getAmountJan() : BigDecimal.ZERO;
                entity.setAmountJan(currentAmount.add(overdueAmount));
            }
            case "02" -> {
                BigDecimal currentAmount = entity.getAmountFeb() != null ? entity.getAmountFeb() : BigDecimal.ZERO;
                entity.setAmountFeb(currentAmount.add(overdueAmount));
            }
            case "03" -> {
                BigDecimal currentAmount = entity.getAmountMar() != null ? entity.getAmountMar() : BigDecimal.ZERO;
                entity.setAmountMar(currentAmount.add(overdueAmount));
            }
            case "04" -> {
                BigDecimal currentAmount = entity.getAmountApr() != null ? entity.getAmountApr() : BigDecimal.ZERO;
                entity.setAmountApr(currentAmount.add(overdueAmount));
            }
            case "05" -> {
                BigDecimal currentAmount = entity.getAmountMay() != null ? entity.getAmountMay() : BigDecimal.ZERO;
                entity.setAmountMay(currentAmount.add(overdueAmount));
            }
            case "06" -> {
                BigDecimal currentAmount = entity.getAmountJun() != null ? entity.getAmountJun() : BigDecimal.ZERO;
                entity.setAmountJun(currentAmount.add(overdueAmount));
            }
            case "07" -> {
                BigDecimal currentAmount = entity.getAmountJul() != null ? entity.getAmountJul() : BigDecimal.ZERO;
                entity.setAmountJul(currentAmount.add(overdueAmount));
            }
            case "08" -> {
                BigDecimal currentAmount = entity.getAmountAug() != null ? entity.getAmountAug() : BigDecimal.ZERO;
                entity.setAmountAug(currentAmount.add(overdueAmount));
            }
            case "09" -> {
                BigDecimal currentAmount = entity.getAmountSep() != null ? entity.getAmountSep() : BigDecimal.ZERO;
                entity.setAmountSep(currentAmount.add(overdueAmount));
            }
            case "10" -> {
                BigDecimal currentAmount = entity.getAmountOct() != null ? entity.getAmountOct() : BigDecimal.ZERO;
                entity.setAmountOct(currentAmount.add(overdueAmount));
            }
            case "11" -> {
                BigDecimal currentAmount = entity.getAmountNov() != null ? entity.getAmountNov() : BigDecimal.ZERO;
                entity.setAmountNov(currentAmount.add(overdueAmount));
            }
            case "12" -> {
                BigDecimal currentAmount = entity.getAmountDec() != null ? entity.getAmountDec() : BigDecimal.ZERO;
                entity.setAmountDec(currentAmount.add(overdueAmount));
            }
            default -> throw new IllegalArgumentException("无效的月份: " + month);
        }
    }

    /**
     * 尝试解析日期字符串，支持多种日期格式：yyyy-MM-dd、yyyy年MM月、yyyy年MM月dd日。
     *
     * @param dateStr 日期字符串
     * @return 解析后的 Date 对象
     * @throws ParseException 当所有格式均解析失败时抛出异常
     */
    private Date parseDueDate(String dateStr) throws ParseException {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        String[] dateFormats = {"yyyy-MM-dd", "yyyy年MM月", "yyyy年MM月dd日"};
        for (String format : dateFormats) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            try {
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // 当前格式解析失败，继续下一种格式
            }
        }
        throw new ParseException("无法解析日期: " + dateStr, 0);
    }
}