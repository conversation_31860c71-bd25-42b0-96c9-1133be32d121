package com.laoshu198838.service;

import com.laoshu198838.model.overduedebt.dto.entity.DebtStatisticsDetailDTO;
import com.laoshu198838.model.overduedebt.dto.query.DebtStatisticsDTO;
import com.laoshu198838.repository.overdue_debt.OverdueDebtSummaryRepository;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class OverdueDebtService {

    private final OverdueDebtSummaryRepository overdueDebtSummaryRepository;
    private final DebtDetailsExportRepository debtDetailsExportRepository;
    private final OverdueDebtDetailRepository overdueDebtDetailRepository;

    public OverdueDebtService(OverdueDebtSummaryRepository overdueDebtSummaryRepository,
                             DebtDetailsExportRepository debtDetailsExportRepository,
                             OverdueDebtDetailRepository overdueDebtDetailRepository) {
        this.overdueDebtSummaryRepository = overdueDebtSummaryRepository;
        this.debtDetailsExportRepository = debtDetailsExportRepository;
        this.overdueDebtDetailRepository = overdueDebtDetailRepository;
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDTO getDebtStatistics(String year, String month, String company) {
        DebtStatisticsDTO dto = new DebtStatisticsDTO();
//        逾期债权总览数据
        dto.setTotalReductionAmount(overdueDebtSummaryRepository.findTotalReductionAmount());
        dto.setTotalDebtBalance(overdueDebtSummaryRepository.findTotalDebtBalance());
//        存量债权相关数据
        dto.setInitialDebtBalance(overdueDebtSummaryRepository.findInitialDebtBalance());
        dto.setInitialDebtReductionAmount(overdueDebtSummaryRepository.findInitialDebtReductionAmount());
        dto.setInitialDebtEndingBalance(overdueDebtSummaryRepository.findInitialDebtEndingBalance());
//      新增债权相关数据
        dto.setNewDebtAmount(overdueDebtSummaryRepository.findNewDebtAmount());
        dto.setNewDebtReductionAmount(overdueDebtSummaryRepository.findNewDebtReductionAmount());
        dto.setNewDebtBalance(overdueDebtSummaryRepository.findNewDebtBalance());
//        逾期债权按管理公司汇总数据
        dto.setNewDebtSummaryByCompany(overdueDebtSummaryRepository.findNewDebtSummaryByCompany(year));
        dto.setExistingDebtSummaryByCompany(overdueDebtSummaryRepository.findExistingDebtSummaryByCompany(year));
//        逾期债权月度数据
        dto.setMonthNewReductionDebtByCompany(overdueDebtSummaryRepository.findMonthNewReductionDebtByCompany(year, month, company));

        return dto;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public DebtStatisticsDetailDTO getDebtStatisticsDetail(String year, String month, String company) {

        DebtStatisticsDetailDTO detailDto = new DebtStatisticsDetailDTO();

        // 获取债务数据的详细信息
        detailDto.setNewDebtDetailList(debtDetailsExportRepository.findNewDebtDetailList(year, month, company));
        // 使用OverdueDebtDetailRepository来计算累计处置金额，而不是简单的每月处置金额
        detailDto.setReductionDebtDetailList(overdueDebtDetailRepository.findReductionDebtDetailList(year, month, company));

        return detailDto;
    }

}
