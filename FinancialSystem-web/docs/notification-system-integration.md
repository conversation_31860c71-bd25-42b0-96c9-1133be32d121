# 弹窗通知系统集成指南

## 概述

本项目已升级为统一的弹窗通知系统，所有弹窗都会在页面正中央显示，并根据行业标准自动消失。

## 主要特性

### 1. 居中显示
- 所有弹窗都在屏幕正中央显示
- 响应式设计，适配不同屏幕尺寸
- 优雅的动画效果

### 2. 行业标准自动消失时长
- **成功消息**: 3秒 - 用户确认操作成功
- **信息消息**: 4秒 - 给用户足够阅读时间
- **警告消息**: 5秒 - 需要用户注意
- **错误消息**: 6秒 - 重要信息需要充分注意
- **表单验证**: 7秒 - 需要用户修正错误
- **网络错误**: 8秒 - 重要错误信息
- **Toast提示**: 2秒 - 快速反馈
- **确认对话框**: 不自动消失，需要用户操作

## 快速开始

### 1. 在应用根组件中添加Provider

```jsx
// App.js
import { NotificationProvider } from 'components/feedback/NotificationManager';

function App() {
  return (
    <NotificationProvider>
      {/* 你的应用组件 */}
    </NotificationProvider>
  );
}
```

### 2. 在组件中使用通知

```jsx
import { useNotifications } from 'components/feedback/NotificationManager';

function MyComponent() {
  const notifications = useNotifications();

  const handleSuccess = () => {
    notifications.success('操作成功完成！');
  };

  const handleError = () => {
    notifications.error('操作失败，请重试');
  };

  const handleConfirm = () => {
    notifications.confirm('确定要删除吗？', {
      onConfirm: () => {
        // 执行删除操作
        notifications.success('删除成功');
      }
    });
  };

  return (
    <div>
      <button onClick={handleSuccess}>成功通知</button>
      <button onClick={handleError}>错误通知</button>
      <button onClick={handleConfirm}>确认对话框</button>
    </div>
  );
}
```

## API 参考

### 基础方法

```jsx
const notifications = useNotifications();

// 显示成功消息
notifications.success(message, options);

// 显示错误消息
notifications.error(message, options);

// 显示警告消息
notifications.warning(message, options);

// 显示信息消息
notifications.info(message, options);

// 显示确认对话框
notifications.confirm(message, options);

// 显示Toast提示
notifications.toast(message, type, duration);
```

### 选项参数

```jsx
const options = {
  title: '标题',           // 通知标题
  duration: 5000,         // 自定义显示时长（毫秒）
  onConfirm: () => {},    // 确认回调
  onCancel: () => {},     // 取消回调
  confirmText: '确认',    // 确认按钮文本
  cancelText: '取消',     // 取消按钮文本
  showCancel: true,       // 是否显示取消按钮
};
```

## 现有组件升级

### 1. MDSnackbar 组件升级

现有的 MDSnackbar 组件已升级支持：
- 居中显示（添加 `centered={true}` 属性）
- 行业标准自动消失时长

```jsx
// 旧用法
<MDSnackbar
  color="success"
  autoHideDuration={5000}
  // ...其他属性
/>

// 新用法 - 居中显示 + 自动时长
<MDSnackbar
  color="success"
  centered={true}
  // autoHideDuration 会根据 color 自动设置
  // ...其他属性
/>
```

### 2. 替换现有弹窗

#### 替换 window.alert
```jsx
// 旧代码
window.alert('操作成功');

// 新代码
notifications.success('操作成功');
```

#### 替换 window.confirm
```jsx
// 旧代码
if (window.confirm('确定要删除吗？')) {
  deleteItem();
}

// 新代码
notifications.confirm('确定要删除吗？', {
  onConfirm: () => deleteItem()
});
```

#### 替换自定义Toast
```jsx
// 旧代码
showToast('操作完成', 2000);

// 新代码
notifications.toast('操作完成');
```

## 业务场景示例

### 1. 表单提交
```jsx
const handleSubmit = async (formData) => {
  try {
    // 显示加载状态
    const loadingId = notifications.showNotification('info', '正在提交...', {
      duration: 0 // 不自动消失
    });

    await submitForm(formData);
    
    // 隐藏加载状态
    notifications.hideNotification(loadingId);
    
    // 显示成功消息
    notifications.success('提交成功！');
    
  } catch (error) {
    notifications.hideNotification(loadingId);
    notifications.error('提交失败：' + error.message);
  }
};
```

### 2. 数据验证
```jsx
const validateForm = (data) => {
  const errors = [];
  
  if (!data.name) errors.push('姓名');
  if (!data.email) errors.push('邮箱');
  
  if (errors.length > 0) {
    notifications.warning(`请填写必填字段：${errors.join('、')}`, {
      title: '表单验证',
      duration: 7000 // 验证错误需要更长时间
    });
    return false;
  }
  
  return true;
};
```

### 3. 网络错误处理
```jsx
const handleNetworkError = (error) => {
  if (error.code === 'NETWORK_ERROR') {
    notifications.error('网络连接超时，请检查网络设置后重试', {
      title: '网络错误',
      duration: 8000 // 网络错误需要更长时间
    });
  }
};
```

## 最佳实践

### 1. 消息文案
- 使用简洁明了的语言
- 提供具体的操作指导
- 避免技术术语

### 2. 时长设置
- 遵循行业标准时长
- 重要信息适当延长显示时间
- 简单反馈使用短时长

### 3. 用户体验
- 避免同时显示多个相同类型的通知
- 重要操作使用确认对话框
- 提供明确的操作反馈

## 注意事项

1. **性能考虑**: 避免在短时间内创建大量通知
2. **可访问性**: 系统已内置ARIA标签和键盘导航支持
3. **响应式**: 在移动设备上会自动调整显示方式
4. **主题适配**: 自动适配明暗主题

## 迁移清单

- [ ] 在App.js中添加NotificationProvider
- [ ] 替换所有window.alert调用
- [ ] 替换所有window.confirm调用
- [ ] 更新现有的MDSnackbar使用方式
- [ ] 统一错误处理中的通知显示
- [ ] 更新表单验证的错误提示
- [ ] 测试各种场景下的通知显示效果
