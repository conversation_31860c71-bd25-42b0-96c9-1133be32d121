**目的**: 专业调试和问题解决

---

@include ../../.claude/commands/shared/universal-constants.yml#Universal_Legend

## 命令执行

执行: 立即执行。--plan→ 先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

使用根本原因分析和基于证据的解决方案系统地调试和解决 $ARGUMENTS 中的问题。

@include ../../.claude/commands/shared/flag-inheritance.yml#Universal_Always

示例:

- `/troubleshoot "应用启动时崩溃"` - 调试崩溃
- `/troubleshoot --performance "API响应慢"` - 性能问题
- `/troubleshoot --interactive "登录失败"` - 引导式调试

## 命令特定标志

--performance: "专注于性能瓶颈"
--memory: "内存泄漏检测和分析"
--network: "网络相关调试"
--interactive: "逐步引导式故障排除"
--trace: "启用详细执行跟踪"
--bisect: "Git 二分查找破坏性提交"

## 故障排除方法

**1. 重现:** 隔离最小重现步骤 | 记录步骤 | 验证一致性 | 捕获完整上下文

**2. 收集证据:** 错误消息和堆栈跟踪 | 日志和指标 | 系统状态 | 最近更改 | 环境差异

**3. 形成假设:** 最可能原因 | 替代解释 | 测试预测 | 排除可能性

**4. 测试验证:** 针对性实验 | 改变单一变量 | 记录结果 | 确认根本原因

**5. 修复预防:** 实施解决方案 | 添加测试 | 记录修复 | 防止复发

## 常见问题类别

**性能:** 慢查询 | 内存泄漏 | CPU 瓶颈 | 网络延迟 | 低效算法

**崩溃/错误:** 空引用 | 类型不匹配 | 竞争条件 | 内存损坏 | 栈溢出

**集成:** API 失败 | 认证问题 | 版本冲突 | 配置问题 | 网络超时

**数据问题:** 损坏 | 不一致 | 迁移失败 | 编码问题 | 并发冲突

@include ../../.claude/commands/shared/quality-patterns.yml#Root_Cause_Analysis

## 交付物

**根本原因报告:** 问题描述 | 收集的证据 | 分析过程 | 识别根本原因 | 实施修复

**修复文档:** 什么被破坏 | 为什么破坏 | 如何修复 | 预防措施 | 添加的测试用例

**知识库:** 问题 → 解决方案映射 | 故障排除指南 | 常见模式 | 预防检查清单

@include ../../.claude/commands/shared/universal-constants.yml#Standard_Messages_Templates
