**目的**: 全面测试框架

---

@include ../../.claude/commands/shared/universal-constants.yml#Universal_Legend

## 命令执行

执行: 立即执行。--plan→ 先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

使用现代测试框架和方法为 $ARGUMENTS 中指定的代码创建或运行全面测试套件。

@include ../../.claude/commands/shared/flag-inheritance.yml#Universal_Always

示例:

- `/test --tdd` - 测试驱动开发工作流
- `/test --coverage` - 生成覆盖率报告
- `/test --watch` - 持续测试模式
- `/test --integration` - 运行集成测试
- `/test --e2e` - 运行端到端测试

## 命令特定标志

--tdd: "测试驱动开发工作流（编写失败测试 → 实现 → 通过）"
--coverage: "生成详细覆盖率报告及未覆盖行"
--integration: "运行跨组件/服务的集成测试"
--e2e: "使用真实浏览器/环境运行端到端测试"
--unit: "仅关注单元测试（默认）"
--mutation: "运行变异测试验证测试质量"
--snapshot: "更新/验证快照测试"
--watch: "文件更改时持续运行测试"
--bail: "首次测试失败时停止"
--parallel: "并行工作器运行测试"
--update-snapshots: "更新所有快照测试"

## 测试方法

**单元测试:** 隔离组件/函数测试 | 模拟所有依赖 | 快速执行 | 高覆盖率目标

**集成测试:** 组件交互测试 | 真实服务调用 | 数据库事务 | API 端点测试

**端到端测试:** 完整用户工作流 | 浏览器自动化 | 真实环境 | 关键路径覆盖

**TDD 工作流:** 红 → 绿 → 重构循环 | 编写最小代码 | 全面覆盖 | 设计涌现

## 测试模式

**测试结构:** 准备-执行-断言（AAA） | 给定-当时-那么（BDD） | 设置 → 执行 → 验证 → 拆除

**覆盖率目标:** 语句: 80%+ | 分支: 75%+ | 函数: 90%+ | 行: 80%+

**测试组织:**

- `__tests__/` 或 `test/` 目录
- `*.test.{js,ts}` 或 `*.spec.{js,ts}` 命名
- 测试目录中镜像源结构
- 按功能或组件分组

@include ../../.claude/commands/shared/quality-patterns.yml#Test_Quality_Standards

## 框架支持

**JavaScript/TypeScript:** Jest（默认） | Mocha + Chai | Vitest | Testing Library

**Python:** pytest（默认） | unittest | nose2 | doctest

**Go:** 内置测试 | Testify | Ginkgo/Gomega

**Java:** JUnit 5 | TestNG | Mockito | Spring Test

**其他:** 特定框架最佳实践 | 原生测试运行器

## 交付物

**测试文件:** 在适当测试目录中创建 | 遵循命名约定 | 全面测试用例

**覆盖率报告:** `coverage/` 中的 HTML 报告 | 控制台摘要 | 未覆盖行识别

**CI 配置:** GitHub Actions | CircleCI | Jenkins | GitLab CI

**文档:** 测试计划 | 测试用例 | 覆盖率目标 | CI/CD 集成

@include ../../.claude/commands/shared/universal-constants.yml#Standard_Messages_Templates
