import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
} from '@mui/material';
import { Visibility as VisibilityIcon, Delete as DeleteIcon } from '@mui/icons-material';
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import GenericDataTable from 'components/tables/GenericDataTable';
import api from 'utils/api';

/**
 * 用户权限列表组件
 */
function UserPermissionsList({ onError }) {
  const [users, setUsers] = useState([]);
  const [rowsPerPage] = useState(10);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userPermissions, setUserPermissions] = useState([]);
  const [permissionDialogOpen, setPermissionDialogOpen] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [companyFilter, setCompanyFilter] = useState('');
  const [companies, setCompanies] = useState([]);

  useEffect(() => {
    fetchUsers();
    fetchCompanies();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await api.get('/users');
      setUsers(response.data || []);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      onError && onError('获取用户列表失败：' + (error.response?.data?.message || error.message));
    }
  };

  const fetchCompanies = async () => {
    try {
      const response = await api.get('/companies');
      setCompanies(response.data?.data || []);
    } catch (error) {
      console.error('获取公司列表失败:', error);
    }
  };

  const fetchUserPermissions = async userId => {
    try {
      const response = await api.get(`/data-permissions/user-permissions?userId=${userId}`);
      setUserPermissions(response.data?.data || []);
    } catch (error) {
      console.error('获取用户权限失败:', error);
      onError && onError('获取用户权限失败：' + (error.response?.data?.message || error.message));
    }
  };

  const handleViewPermissions = async user => {
    setSelectedUser(user);
    await fetchUserPermissions(user.id);
    setPermissionDialogOpen(true);
  };

  const handleRevokePermission = async (permissionId, userId, companyId) => {
    if (!window.confirm('确定要撤销该权限吗？')) {
      return;
    }

    try {
      await api.post('/data-permissions/revoke', {
        userId: userId,
        companyId: companyId,
      });

      // 刷新权限列表
      await fetchUserPermissions(selectedUser.id);

      alert('权限撤销成功');
    } catch (error) {
      console.error('撤销权限失败:', error);
      onError && onError('撤销权限失败：' + (error.response?.data?.message || error.message));
    }
  };

  const getPermissionTypeLabel = type => {
    const typeMap = {
      read: '只读',
      write: '读写',
      admin: '管理员',
    };
    return typeMap[type] || type;
  };

  const getPermissionTypeColor = type => {
    const colorMap = {
      read: 'info',
      write: 'success',
      admin: 'error',
    };
    return colorMap[type] || 'default';
  };

  const getStatusLabel = status => {
    const statusMap = {
      active: '激活',
      suspended: '暂停',
      expired: '过期',
    };
    return statusMap[status] || status;
  };

  const getStatusColor = status => {
    const colorMap = {
      active: 'success',
      suspended: 'warning',
      expired: 'error',
    };
    return colorMap[status] || 'default';
  };

  // 过滤用户列表
  const filteredUsers = users.filter(user => {
    const matchesKeyword =
      !searchKeyword ||
      user.username?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      user.name?.toLowerCase().includes(searchKeyword.toLowerCase());

    const matchesCompany = !companyFilter || user.company === companyFilter;

    return matchesKeyword && matchesCompany;
  });

  return (
    <MDBox>
      {/* 搜索和筛选 */}
      <MDBox display="flex" gap={2} mb={3} flexWrap="wrap">
        <TextField
          label="搜索用户"
          variant="outlined"
          size="small"
          value={searchKeyword}
          onChange={e => setSearchKeyword(e.target.value)}
          placeholder="输入用户名或姓名"
          sx={{ minWidth: 200 }}
        />

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>筛选公司</InputLabel>
          <Select
            value={companyFilter}
            onChange={e => setCompanyFilter(e.target.value)}
            label="筛选公司"
          >
            <MenuItem value="">全部公司</MenuItem>
            {companies.map(company => (
              <MenuItem key={company.id} value={company.companyName}>
                {company.companyName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <MDButton variant="outlined" color="info" onClick={fetchUsers}>
          刷新列表
        </MDButton>
      </MDBox>

      {/* 用户列表表格 */}
      <GenericDataTable
        columns={[
          { field: 'username', headerName: '用户名', width: '15%' },
          { field: 'name', headerName: '姓名', width: '15%' },
          { field: 'company', headerName: '公司', width: '20%' },
          { field: 'department', headerName: '部门', width: '15%' },
          { field: 'role', headerName: '角色', width: '15%' },
          { field: 'status', headerName: '状态', width: '10%' },
        ]}
        data={filteredUsers.map(user => ({
          ...user,
          status: (
            <Chip
              label={user.status}
              color={user.status === 'ACTIVE' ? 'success' : 'default'}
              size="small"
            />
          ),
        }))}
        pageSize={rowsPerPage}
        fontSize={20}
        renderActions={user => (
          <IconButton size="small" onClick={() => handleViewPermissions(user)} title="查看权限">
            <VisibilityIcon fontSize="small" />
          </IconButton>
        )}
        actionColumnWidth="10%"
        actionColumnTitle="操作"
      />

      {/* 用户权限详情对话框 */}
      <Dialog
        open={permissionDialogOpen}
        onClose={() => setPermissionDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          用户权限详情 - {selectedUser?.name} ({selectedUser?.username})
        </DialogTitle>
        <DialogContent>
          <MDBox mt={2}>
            <MDTypography variant="h6" gutterBottom>
              基本信息
            </MDTypography>
            <MDTypography variant="body2" gutterBottom>
              所属公司：{selectedUser?.company} | 部门：{selectedUser?.department}
            </MDTypography>

            <MDTypography variant="h6" gutterBottom sx={{ mt: 3 }}>
              数据访问权限
            </MDTypography>

            {userPermissions.length === 0 ? (
              <Alert severity="info">该用户暂无数据访问权限</Alert>
            ) : (
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>公司</TableCell>
                      <TableCell>权限类型</TableCell>
                      <TableCell>是否默认</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>过期时间</TableCell>
                      <TableCell>授权人</TableCell>
                      <TableCell align="center">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userPermissions.map(permission => (
                      <TableRow key={permission.id}>
                        <TableCell>{permission.company?.companyName}</TableCell>
                        <TableCell>
                          <Chip
                            label={getPermissionTypeLabel(permission.permissionType)}
                            color={getPermissionTypeColor(permission.permissionType)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{permission.isDefault ? '是' : '否'}</TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusLabel(permission.status)}
                            color={getStatusColor(permission.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {permission.expiresAt
                            ? new Date(permission.expiresAt).toLocaleDateString()
                            : '永久'}
                        </TableCell>
                        <TableCell>{permission.grantedBy?.username || '-'}</TableCell>
                        <TableCell align="center">
                          {!permission.isDefault && permission.status === 'active' && (
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() =>
                                handleRevokePermission(
                                  permission.id,
                                  permission.user?.id,
                                  permission.company?.id,
                                )
                              }
                              title="撤销权限"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </MDBox>
        </DialogContent>
        <DialogActions>
          <MDButton onClick={() => setPermissionDialogOpen(false)}>关闭</MDButton>
        </DialogActions>
      </Dialog>
    </MDBox>
  );
}

UserPermissionsList.propTypes = {
  onError: PropTypes.func,
};

export default UserPermissionsList;
