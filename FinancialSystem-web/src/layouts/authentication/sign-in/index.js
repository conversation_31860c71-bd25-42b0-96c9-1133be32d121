import { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import api from '../../../utils/api';

// @mui material components
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import MuiLink from '@mui/material/Link';

// @mui icons
import FacebookIcon from '@mui/icons-material/Facebook';
import GitHubIcon from '@mui/icons-material/GitHub';
import GoogleIcon from '@mui/icons-material/Google';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';

// Authentication layout components
import BasicLayout from 'layouts/authentication/components/BasicLayout';

// Images
import bgImage from 'assets/images/bg-sign-in-basic.jpeg';

// layouts/authentication/sign-in/Basic.js
import { jwtDecode } from 'jwt-decode';

function Basic() {
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });

  // 检查URL参数中是否有错误消息
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const message = queryParams.get('message');

    if (message) {
      setError(message);

      // 更新URL，清除错误参数
      const newUrl = location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [location]);

  const handleSubmit = async e => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // 清除存储的认证数据
      localStorage.removeItem('auth');
      localStorage.removeItem('token');

      // 发送登录请求
      const response = await api.post('/auth/login', formData);

      if (!response || !response.data || !response.data.token) {
        throw new Error('服务器响应无效');
      }

      const token = response.data.token;

      // 验证token
      const decoded = jwtDecode(token);
      if (!decoded || decoded.exp * 1000 < Date.now()) {
        throw new Error('令牌已过期或无效');
      }

      // 构建用户数据 - 确保包含所有必要字段
      console.log(
        '构建用户数据...:',
        decoded.sub,
        decoded.roles,
        decoded.name,
        decoded.companyname,
        decoded.department,
      );
      const userData = {
        username: decoded.sub || formData.username,
        token: token,
        role: Array.isArray(decoded.roles) ? decoded.roles[0] : decoded.roles || 'USER',
        // 添加必要的额外字段，从令牌获取或使用默认值
        name: decoded.name || decoded.sub || '用户',
        company: decoded.companyname || '万润科技',
        department: decoded.department || '资产财务部',
        // 添加时间戳，便于跟踪会话状态
        loginTime: Date.now(),
      };

      // 首先设置 token，这是最重要的
      localStorage.setItem('token', token);
      // 然后保存完整用户数据
      localStorage.setItem('auth', JSON.stringify(userData));

      // 验证数据是否成功保存
      const savedToken = localStorage.getItem('token');
      const savedAuth = localStorage.getItem('auth');
      console.log(
        '验证保存的数据:',
        savedToken ? '令牌已保存' : '令牌未保存',
        savedAuth ? '用户数据已保存' : '用户数据未保存',
      );

      // 更新认证状态
      login(userData);

      // 导航到目标页面
      const targetPath = '/debt-management/Overdue-statistics';

      navigate(targetPath, { replace: true });
    } catch (err) {
      console.error('登录失败:', err);
      setError('用户名或密码错误，请重试');

      // 确保清除所有认证数据
      localStorage.removeItem('auth');
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
    }
  };

  return (
    <BasicLayout image={bgImage}>
      <Card>
        <MDBox
          variant="gradient"
          bgColor="info"
          borderRadius="lg"
          coloredShadow="info"
          mx={2}
          mt={-3}
          p={2}
          mb={1}
          textAlign="center"
        >
          <MDTypography variant="h4" fontWeight="medium" color="white" mt={1}>
            登录
          </MDTypography>
          <Grid container spacing={3} justifyContent="center" sx={{ mt: 1, mb: 2 }}>
            <Grid item xs={2}>
              <MDTypography component={MuiLink} href="#" variant="body1" color="white">
                <FacebookIcon color="inherit" />
              </MDTypography>
            </Grid>
            <Grid item xs={2}>
              <MDTypography component={MuiLink} href="#" variant="body1" color="white">
                <GitHubIcon color="inherit" />
              </MDTypography>
            </Grid>
            <Grid item xs={2}>
              <MDTypography component={MuiLink} href="#" variant="body1" color="white">
                <GoogleIcon color="inherit" />
              </MDTypography>
            </Grid>
          </Grid>
        </MDBox>
        <MDBox pt={2} pb={3} px={4}>
          <MDBox component="form" name="login-form" onSubmit={handleSubmit} mt={3}>
            <MDBox mb={3}>
              <MDInput
                type="text"
                label="用户名"
                fullWidth
                value={formData.username}
                onChange={e => setFormData({ ...formData, username: e.target.value })}
                InputLabelProps={{
                  shrink: true,
                }}
                sx={{
                  '& .MuiInputBase-input': {
                    fontSize: '1rem',
                    padding: '11px 14px',
                    backgroundColor: 'transparent',
                    borderRadius: '8px',
                    height: '22px',
                  },
                  '& .MuiInputLabel-root': {
                    transform: 'translate(14px, 8px) scale(1)',
                    color: '#666',
                    backgroundColor: 'transparent',
                    padding: '0 4px',
                    marginLeft: '-2px',
                    lineHeight: '1.2',
                    fontSize: '14px',
                    display: 'flex',
                    alignItems: 'center',
                    whiteSpace: 'nowrap',
                    zIndex: 1,
                    width: 'auto',
                    maxWidth: 'calc(100% - 24px)',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    pointerEvents: 'none',
                    transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&.Mui-focused, &.MuiFormLabel-filled': {
                      transform: 'translate(14px, -9px) scale(0.85)',
                      backgroundColor: 'transparent',
                      padding: '0 4px',
                    },
                    '&.Mui-focused': {
                      color: '#1A73E8',
                    },
                  },
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    backgroundColor: '#F5F7FB',
                    marginTop: '8px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                    transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    '& fieldset': {
                      borderColor: '#E0E3E7',
                      borderWidth: '1px',
                      top: '-5px',
                      transition: 'border-color 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    },
                    '&:hover': {
                      backgroundColor: '#FFFFFF',
                      boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
                      '& fieldset': {
                        borderColor: '#B2BAC2',
                        borderWidth: '1px',
                      },
                    },
                    '&.Mui-focused': {
                      backgroundColor: '#FFFFFF',
                      boxShadow: '0 2px 8px rgba(26,115,232,0.15)',
                      '& fieldset': {
                        borderColor: '#1A73E8',
                        borderWidth: '2px',
                      },
                    },
                  },
                }}
              />
            </MDBox>
            <MDBox mb={3}>
              <MDInput
                type="password"
                label="密码"
                fullWidth
                value={formData.password}
                onChange={e => setFormData({ ...formData, password: e.target.value })}
                InputLabelProps={{
                  shrink: true,
                }}
                sx={{
                  '& .MuiInputBase-input': {
                    fontSize: '1rem',
                    padding: '11px 14px',
                    backgroundColor: 'transparent',
                    borderRadius: '8px',
                    height: '22px',
                  },
                  '& .MuiInputLabel-root': {
                    transform: 'translate(14px, 8px) scale(1)',
                    color: '#666',
                    backgroundColor: 'transparent',
                    padding: '0 4px',
                    marginLeft: '-2px',
                    lineHeight: '1.2',
                    fontSize: '14px',
                    display: 'flex',
                    alignItems: 'center',
                    whiteSpace: 'nowrap',
                    zIndex: 1,
                    width: 'auto',
                    maxWidth: 'calc(100% - 24px)',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    pointerEvents: 'none',
                    transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&.Mui-focused, &.MuiFormLabel-filled': {
                      transform: 'translate(14px, -9px) scale(0.85)',
                      backgroundColor: 'transparent',
                      padding: '0 4px',
                    },
                    '&.Mui-focused': {
                      color: '#1A73E8',
                    },
                  },
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    backgroundColor: '#F5F7FB',
                    marginTop: '8px',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                    transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    '& fieldset': {
                      borderColor: '#E0E3E7',
                      borderWidth: '1px',
                      top: '-5px',
                      transition: 'border-color 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
                    },
                    '&:hover': {
                      backgroundColor: '#FFFFFF',
                      boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
                      '& fieldset': {
                        borderColor: '#B2BAC2',
                        borderWidth: '1px',
                      },
                    },
                    '&.Mui-focused': {
                      backgroundColor: '#FFFFFF',
                      boxShadow: '0 2px 8px rgba(26,115,232,0.15)',
                      '& fieldset': {
                        borderColor: '#1A73E8',
                        borderWidth: '2px',
                      },
                    },
                  },
                }}
              />
            </MDBox>
            {error && (
              <MDTypography variant="caption" color="error" fontWeight="light">
                {error}
              </MDTypography>
            )}
            <MDBox mt={5} mb={1}>
              <MDButton variant="gradient" color="info" fullWidth type="submit" disabled={loading}>
                {loading ? '登录中...' : '登录'}
              </MDButton>
            </MDBox>
            <MDBox mt={3} mb={1} textAlign="center">
              <MDTypography variant="button" color="text">
                Don&apos;t have an account?{' '}
                <MDTypography
                  component={Link}
                  to="/authentication/sign-up"
                  variant="button"
                  color="info"
                  fontWeight="medium"
                  textGradient
                >
                  Sign up
                </MDTypography>
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
      </Card>
    </BasicLayout>
  );
}

export default Basic;
