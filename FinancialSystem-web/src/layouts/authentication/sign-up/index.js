import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import Card from '@mui/material/Card';
import Checkbox from '@mui/material/Checkbox';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDInput from 'components/MDInput';
import MDButton from 'components/MDButton';
import CoverLayout from 'layouts/authentication/components/CoverLayout';
import bgImage from 'assets/images/bg-sign-up-cover.jpeg';
import { MenuItem, Select, InputLabel, FormControl } from '@mui/material';

/**
=========================================================
* Material Dashboard 2 React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

function Cover() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    name: '',
    company: '',
    department: '资产财务部',
    passwordConfirm: '',
    agreeToTerms: false,
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // 添加公司列表
  const companies = [
    '万润科技',
    '日上光电',
    '恒润光电',
    '信立传媒',
    '万润半导体',
    '中筑天佑',
    '亿万无线',
    '万润新能源',
    '湖北万润',
    '长江万润',
    '长春万润',
    '万象新动',
  ];

  const handleSubmit = async e => {
    e.preventDefault();
    if (!formData.agreeToTerms) {
      setError('请同意服务条款');
      return;
    }

    // 验证密码匹配
    if (formData.password !== formData.passwordConfirm) {
      setError('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    setError('');

    // 构造要发送的数据对象
    const registrationData = {
      username: formData.username,
      password: formData.password,
      passwordConfirm: formData.passwordConfirm,
      name: formData.name,
      company: formData.company,
      department: formData.department,
    };

    try {
      const apiBaseUrl = process.env.REACT_APP_API_BASE_URL || '/api';
      const response = await fetch(`${apiBaseUrl}/users/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(registrationData),
      });

      // 先获取响应文本
      const responseText = await response.text();

      let data;
      try {
        // 尝试解析响应JSON
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('响应解析失败:', parseError);
        throw new Error('服务器响应格式错误');
      }

      if (!response.ok) {
        // 根据状态码处理不同的错误情况
        switch (response.status) {
        case 400:
          throw new Error(data.message || '请求数据格式错误');
        case 403:
          throw new Error(data.message || '没有权限执行此操作');
        case 409:
          throw new Error(data.message || '用户名已存在');
        default:
          throw new Error(data.message || '注册失败');
        }
      }

      // 注册成功

      navigate('/authentication/sign-in');
    } catch (err) {
      console.error('注册过程出错:', err);
      setError(err.message || '注册失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <CoverLayout image={bgImage}>
      <Card>
        <MDBox
          variant="gradient"
          bgColor="info"
          borderRadius="lg"
          coloredShadow="success"
          mx={2}
          mt={-3}
          p={3}
          mb={1}
          textAlign="center"
        >
          <MDTypography variant="h4" fontWeight="medium" color="white" mt={1}>
            欢迎注册
          </MDTypography>
          <MDTypography display="block" variant="button" color="white" my={1}>
            请填写以下信息完成注册
          </MDTypography>
        </MDBox>
        <MDBox pt={4} pb={3} px={3}>
          <MDBox component="form" role="form" onSubmit={handleSubmit}>
            <MDBox mb={2}>
              <MDInput
                type="text"
                label="用户名"
                variant="standard"
                fullWidth
                value={formData.username}
                onChange={e => setFormData({ ...formData, username: e.target.value })}
                required
                placeholder="请输入用户名"
              />
            </MDBox>
            <MDBox mb={2}>
              <MDInput
                type="text"
                label="姓名"
                variant="standard"
                fullWidth
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                required
                placeholder="请输入真实姓名"
              />
            </MDBox>
            <MDBox mb={2}>
              <FormControl fullWidth variant="standard">
                <InputLabel id="company-select-label">管理公司名称</InputLabel>
                <Select
                  labelId="company-select-label"
                  value={formData.company}
                  onChange={e => setFormData({ ...formData, company: e.target.value })}
                  required
                  label="公司名称"
                >
                  {companies.map(company => (
                    <MenuItem key={company} value={company}>
                      {company}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </MDBox>
            <MDBox mb={2}>
              <MDInput
                type="text"
                label="部门"
                variant="standard"
                fullWidth
                value={formData.department}
                onChange={e => setFormData({ ...formData, department: e.target.value })}
                required
                placeholder="请输入所在部门"
                disabled
              />
            </MDBox>
            <MDBox mb={2}>
              <MDInput
                type="password"
                label="密码"
                variant="standard"
                fullWidth
                value={formData.password}
                onChange={e => setFormData({ ...formData, password: e.target.value })}
                required
                placeholder="请输入密码"
              />
            </MDBox>
            <MDBox mb={2}>
              <MDInput
                type="password"
                label="确认密码"
                variant="standard"
                fullWidth
                value={formData.passwordConfirm}
                onChange={e => setFormData({ ...formData, passwordConfirm: e.target.value })}
                required
                placeholder="请再次输入密码"
              />
            </MDBox>
            <MDBox display="flex" alignItems="center" ml={-1}>
              <Checkbox
                checked={formData.agreeToTerms}
                onChange={e => setFormData({ ...formData, agreeToTerms: e.target.checked })}
              />
              <MDTypography
                variant="button"
                fontWeight="regular"
                color="text"
                sx={{ cursor: 'pointer', userSelect: 'none', ml: -1 }}
              >
                &nbsp;&nbsp;我已阅读并同意&nbsp;
              </MDTypography>
              <MDTypography
                component="a"
                href="#"
                variant="button"
                fontWeight="bold"
                color="info"
                textGradient
              >
                用户协议和隐私政策
              </MDTypography>
            </MDBox>
            {error && (
              <MDBox mt={2}>
                <MDTypography variant="caption" color="error" fontWeight="light">
                  {error}
                </MDTypography>
              </MDBox>
            )}
            <MDBox mt={4} mb={1}>
              <MDButton variant="gradient" color="info" fullWidth type="submit" disabled={loading}>
                {loading ? '注册中...' : '立即注册'}
              </MDButton>
            </MDBox>
            <MDBox mt={3} mb={1} textAlign="center">
              <MDTypography variant="button" color="text">
                已有账号？{' '}
                <MDTypography
                  component={Link}
                  to="/authentication/sign-in"
                  variant="button"
                  color="info"
                  fontWeight="medium"
                  textGradient
                >
                  立即登录
                </MDTypography>
              </MDTypography>
            </MDBox>
          </MDBox>
        </MDBox>
      </Card>
    </CoverLayout>
  );
}

export default Cover;
