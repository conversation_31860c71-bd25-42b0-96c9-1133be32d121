import React from 'react';
import { Box, Typography, useTheme, alpha } from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import PropTypes from 'prop-types';

/**
 * 预留空间组件
 * 用于显示未来可添加的导出功能区域，支持自定义图标和现代化设计
 */
const PlaceholderRow = ({ title, icon }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 2,
        p: 2,
        height: '100%',
        justifyContent: 'center',
      }}
    >
      <Box
        sx={{
          width: 64,
          height: 64,
          borderRadius: '50%',
          background: `linear-gradient(135deg,
            ${alpha(theme.palette.primary.main, 0.1)} 0%,
            ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `2px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'scale(1.1)',
            background: `linear-gradient(135deg,
              ${alpha(theme.palette.primary.main, 0.15)} 0%,
              ${alpha(theme.palette.secondary.main, 0.15)} 100%)`,
            border: `2px dashed ${alpha(theme.palette.primary.main, 0.5)}`,
          },
        }}
      >
        {icon ? (
          React.cloneElement(icon, {
            sx: {
              fontSize: 32,
              color: theme.palette.primary.main,
              opacity: 0.7,
            },
          })
        ) : (
          <AddIcon
            sx={{
              fontSize: 32,
              color: theme.palette.primary.main,
              opacity: 0.7,
            }}
          />
        )}
      </Box>
      <Box>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: theme.palette.text.primary,
            opacity: 0.8,
            mb: 0.5,
          }}
        >
          {title}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            opacity: 0.6,
            fontSize: '0.875rem',
            lineHeight: 1.4,
          }}
        >
          即将推出 · 敬请期待
        </Typography>
      </Box>
    </Box>
  );
};

// 属性类型检查
PlaceholderRow.propTypes = {
  title: PropTypes.string.isRequired,
  icon: PropTypes.element,
};

export default PlaceholderRow;
