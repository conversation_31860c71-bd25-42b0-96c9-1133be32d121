import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Divider,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  Warning as WarningIcon,
  FindInPage as FindInPageIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import { useAuth } from 'context/AuthContext';

// 导入子组件
import DataConsistencyCheck from './components/DataConsistencyCheck';
import UpdateFrequencyCard from './components/UpdateFrequencyCard';

const DataMonitor = () => {
  const { user, isAuthenticated, loading } = useAuth();
  const [selectedTab, setSelectedTab] = useState(1);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (user && user.role === 'ROLE_ADMIN') {
      setIsAdmin(true);
    } else {
      setIsAdmin(false);
    }
  }, [user]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  // 样式
  const styles = {
    root: {
      flexGrow: 1,
      py: 3,
    },
    paper: {
      p: 3,
      mb: 4,
      borderRadius: 2,
      backgroundColor: '#FFFFFF',
    },
    title: {
      mb: 2,
      fontWeight: 600,
    },
    subtitle: {
      mb: 2,
      color: 'text.secondary',
    },
  };

  if (loading) {
    return (
      <DashboardLayout>
        <DashboardNavbar />
        <Container maxWidth="lg">
          <Box
            sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}
          >
            <CircularProgress />
          </Box>
        </Container>
      </DashboardLayout>
    );
  }

  if (!isAdmin && isAuthenticated) {
    return (
      <DashboardLayout>
        <DashboardNavbar />
        <Container maxWidth="lg">
          <Box sx={styles.root}>
            <Alert severity="error">您没有权限访问此页面。此页面仅限管理员访问。</Alert>
          </Box>
        </Container>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          {/* 标题 */}
          <Paper sx={styles.paper}>
            <Typography variant="h4" sx={styles.title}>
              数据更新观测平台
            </Typography>
            <Divider sx={{ mb: 3 }} />

            {/* 选项卡 */}
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              textColor="primary"
              indicatorColor="primary"
              sx={{ mb: 3 }}
            >
              <Tab
                label="逾期债权数据"
                icon={<WarningIcon fontSize="small" />}
                iconPosition="start"
                sx={{ textTransform: 'none' }}
              />
              <Tab
                label="数据一致性检查"
                icon={<FindInPageIcon fontSize="small" />}
                iconPosition="start"
                sx={{ textTransform: 'none' }}
              />
              <Tab
                label="其他监控数据"
                icon={<AssessmentIcon fontSize="small" />}
                iconPosition="start"
                sx={{ textTransform: 'none' }}
              />
            </Tabs>

            {/* 基于选项卡显示内容 */}
            {selectedTab === 0 && <Typography variant="h6">逾期债权数据</Typography>}
            {selectedTab === 1 && <DataConsistencyCheck />}
            {selectedTab === 2 && <Typography variant="h6">其他监控数据</Typography>}
          </Paper>

          {/* 数据更新频率和责任人 */}
          <Paper sx={styles.paper}>
            <Typography variant="h4" sx={styles.title}>
              数据更新频率与责任人
            </Typography>
            <Divider sx={{ mb: 4 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <UpdateFrequencyCard
                  title="非诉讼债权数据"
                  icon={<SecurityIcon fontSize="large" />}
                  frequency="每月更新"
                  responsiblePerson="张三"
                  lastUpdate="2023-04-01"
                  type="非诉讼"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <UpdateFrequencyCard
                  title="诉讼债权数据"
                  icon={<SecurityIcon fontSize="large" />}
                  frequency="每季度更新"
                  responsiblePerson="李四"
                  lastUpdate="2023-03-31"
                  type="诉讼"
                />
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default DataMonitor;
