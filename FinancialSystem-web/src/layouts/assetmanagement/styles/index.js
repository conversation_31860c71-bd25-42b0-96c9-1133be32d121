export const styles = {
  root: {
    '& .MuiTextField-root': {
      marginBottom: 2,
    },
  },
  paper: {
    padding: 3,
    margin: '20px 0',
    borderRadius: 2,
    boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 3,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: '1.1rem',
    fontWeight: 500,
    color: '#283593',
    marginBottom: 2,
  },
  card: {
    height: '100%',
    transition: 'transform 0.2s',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
  cardContent: {
    padding: 2,
  },
  statValue: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#1a73e8',
  },
  statLabel: {
    color: '#666',
    marginTop: 1,
  },
  statusChip: {
    marginLeft: 1,
  },
  listItem: {
    '&:hover': {
      backgroundColor: '#f5f5f5',
    },
  },
};
