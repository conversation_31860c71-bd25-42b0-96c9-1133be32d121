import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Assessment as AssessmentIcon,
  Home as HomeIcon,
  Group as GroupIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  Pending as PendingIcon,
} from '@mui/icons-material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

// 定义样式
const styles = {
  root: {
    '& .MuiTextField-root': {
      marginBottom: 2,
    },
  },
  paper: {
    padding: 3,
    margin: '20px 0',
    borderRadius: 2,
    boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 3,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: '1.1rem',
    fontWeight: 500,
    color: '#283593',
    marginBottom: 2,
  },
  card: {
    height: '100%',
    transition: 'transform 0.2s',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
  cardContent: {
    padding: 2,
  },
  statusChip: {
    marginLeft: 1,
  },
  listItem: {
    '&:hover': {
      backgroundColor: '#f5f5f5',
    },
  },
};

// 模拟数据
const mockData = {
  assetReporting: [
    {
      id: 1,
      title: '总部大楼资产报备',
      company: '日上光电',
      date: '2024-03-15',
      status: '已完成',
      description: '总部大楼及附属设施的资产报备材料',
    },
    {
      id: 2,
      title: '研发中心资产报备',
      company: '日上光电',
      date: '2024-03-20',
      status: '进行中',
      description: '研发中心设备及办公设施报备',
    },
  ],
  evaluationRecords: [
    {
      id: 1,
      title: '总部园区资产评估',
      company: '日上光电',
      date: '2024-03-10',
      status: '已完成',
      description: '总部园区土地及建筑物评估报告',
    },
  ],
  propertyRegistration: [
    {
      id: 1,
      title: '总部大楼产权登记',
      company: '日上光电',
      date: '2024-03-05',
      status: '已完成',
      description: '总部大楼产权证书办理',
    },
  ],
  reviewCommittee: [
    {
      id: 1,
      title: '资产处置评审会议',
      company: '日上光电',
      date: '2024-03-25',
      status: '待处理',
      description: '讨论研发中心扩建方案',
    },
  ],
};

const GroupAffairs = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    description: '',
  });

  const handleOpenDialog = module => {
    setSelectedModule(module);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFormData({ title: '', company: '', description: '' });
  };

  const handleSubmit = () => {
    // 这里添加提交逻辑
    handleCloseDialog();
  };

  const renderStatusChip = status => {
    const color = status === '已完成' ? 'success' : status === '进行中' ? 'primary' : 'warning';
    return (
      <Chip
        size="small"
        label={status}
        color={color}
        icon={status === '已完成' ? <CheckCircleIcon /> : <PendingIcon />}
        sx={styles.statusChip}
      />
    );
  };

  const renderModuleCard = (title, icon, items, module) => (
    <Card sx={styles.card}>
      <CardContent sx={styles.cardContent}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {icon}
          <Typography variant="h6" sx={{ ml: 1 }}>
            {title}
          </Typography>
        </Box>
        <List>
          {items.map(item => (
            <ListItem key={item.id} sx={styles.listItem}>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {item.title}
                    {renderStatusChip(item.status)}
                  </Box>
                }
                secondary={
                  <>
                    <Typography component="span" variant="body2" color="text.primary">
                      {item.company} | {item.date}
                    </Typography>
                    <br />
                    {item.description}
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
      <CardActions>
        <Button startIcon={<AddIcon />} onClick={() => handleOpenDialog(module)} color="primary">
          新增
        </Button>
      </CardActions>
    </Card>
  );

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h4" sx={styles.title}>
              对接集团事项
            </Typography>
            <Divider sx={{ mb: 4 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                {renderModuleCard(
                  '资产报备',
                  <DescriptionIcon color="primary" />,
                  mockData.assetReporting,
                  'assetReporting',
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                {renderModuleCard(
                  '评估备案',
                  <AssessmentIcon color="primary" />,
                  mockData.evaluationRecords,
                  'evaluationRecords',
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                {renderModuleCard(
                  '产权登记',
                  <HomeIcon color="primary" />,
                  mockData.propertyRegistration,
                  'propertyRegistration',
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                {renderModuleCard(
                  '资产评审委员会',
                  <GroupIcon color="primary" />,
                  mockData.reviewCommittee,
                  'reviewCommittee',
                )}
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>

      {/* 新增事项对话框 */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          新增
          {selectedModule === 'assetReporting'
            ? '资产报备'
            : selectedModule === 'evaluationRecords'
              ? '评估备案'
              : selectedModule === 'propertyRegistration'
                ? '产权登记'
                : '资产评审'}
          事项
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="标题"
            fullWidth
            value={formData.title}
            onChange={e => setFormData({ ...formData, title: e.target.value })}
          />
          <TextField
            margin="dense"
            label="公司"
            fullWidth
            value={formData.company}
            onChange={e => setFormData({ ...formData, company: e.target.value })}
          />
          <TextField
            margin="dense"
            label="描述"
            fullWidth
            multiline
            rows={4}
            value={formData.description}
            onChange={e => setFormData({ ...formData, description: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>取消</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            提交
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
};

export default GroupAffairs;
