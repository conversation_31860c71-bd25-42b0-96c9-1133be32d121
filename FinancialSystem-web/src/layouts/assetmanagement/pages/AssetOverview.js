import React, { useState } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

// 定义样式
const styles = {
  root: {
    '& .MuiTextField-root': {
      marginBottom: 2,
    },
  },
  paper: {
    padding: 3,
    margin: '20px 0',
    borderRadius: 2,
    boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 3,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: '1.1rem',
    fontWeight: 500,
    color: '#283593',
    marginBottom: 2,
  },
  card: {
    height: '100%',
    transition: 'transform 0.2s',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
  cardContent: {
    padding: 2,
  },
  statValue: {
    fontSize: '1.5rem',
    fontWeight: 600,
    color: '#1a73e8',
  },
  statLabel: {
    color: '#666',
    marginTop: 1,
  },
};

// 模拟数据
const mockData = {
  companies: [
    {
      name: '日上光电',
      assets: {
        totalArea: 50000,
        totalValue: 150000000,
        selfUseArea: 30000,
        rentalArea: 20000,
        properties: [
          {
            name: '总部大楼',
            type: '房产',
            area: 20000,
            purchasePrice: 60000000,
            selfUseArea: 15000,
            rentalArea: 5000,
          },
          {
            name: '研发中心',
            type: '房产',
            area: 15000,
            purchasePrice: 45000000,
            selfUseArea: 10000,
            rentalArea: 5000,
          },
        ],
        lands: [
          {
            name: '总部园区',
            type: '土地',
            area: 15000,
            purchasePrice: 45000000,
            selfUseArea: 5000,
            rentalArea: 10000,
          },
        ],
      },
    },
    // 可以添加更多公司数据
  ],
};

const AssetOverview = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="lg">
        <Box sx={styles.root}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h4" sx={styles.title}>
              公司资产整理情况
            </Typography>
            <Divider sx={{ mb: 4 }} />

            {/* 资产统计卡片 */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={3}>
                <Card sx={styles.card}>
                  <CardContent sx={styles.cardContent}>
                    <Typography variant="h6" sx={styles.statValue}>
                      {mockData.companies[0].assets.totalArea.toLocaleString()}㎡
                    </Typography>
                    <Typography sx={styles.statLabel}>总资产面积</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card sx={styles.card}>
                  <CardContent sx={styles.cardContent}>
                    <Typography variant="h6" sx={styles.statValue}>
                      ¥{(mockData.companies[0].assets.totalValue / 10000).toLocaleString()}万
                    </Typography>
                    <Typography sx={styles.statLabel}>总资产价值</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card sx={styles.card}>
                  <CardContent sx={styles.cardContent}>
                    <Typography variant="h6" sx={styles.statValue}>
                      {mockData.companies[0].assets.selfUseArea.toLocaleString()}㎡
                    </Typography>
                    <Typography sx={styles.statLabel}>自用面积</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card sx={styles.card}>
                  <CardContent sx={styles.cardContent}>
                    <Typography variant="h6" sx={styles.statValue}>
                      {mockData.companies[0].assets.rentalArea.toLocaleString()}㎡
                    </Typography>
                    <Typography sx={styles.statLabel}>租赁面积</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* 详细信息标签页 */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
              <Tabs value={selectedTab} onChange={handleTabChange}>
                <Tab label="房产信息" />
                <Tab label="土地信息" />
              </Tabs>
            </Box>

            {/* 房产信息表格 */}
            {selectedTab === 0 && (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>资产名称</TableCell>
                      <TableCell>类型</TableCell>
                      <TableCell align="right">总面积(㎡)</TableCell>
                      <TableCell align="right">购买价格(万元)</TableCell>
                      <TableCell align="right">自用面积(㎡)</TableCell>
                      <TableCell align="right">租赁面积(㎡)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockData.companies[0].assets.properties.map((property, index) => (
                      <TableRow key={index}>
                        <TableCell>{property.name}</TableCell>
                        <TableCell>{property.type}</TableCell>
                        <TableCell align="right">{property.area.toLocaleString()}</TableCell>
                        <TableCell align="right">
                          {(property.purchasePrice / 10000).toLocaleString()}
                        </TableCell>
                        <TableCell align="right">{property.selfUseArea.toLocaleString()}</TableCell>
                        <TableCell align="right">{property.rentalArea.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* 土地信息表格 */}
            {selectedTab === 1 && (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>资产名称</TableCell>
                      <TableCell>类型</TableCell>
                      <TableCell align="right">总面积(㎡)</TableCell>
                      <TableCell align="right">购买价格(万元)</TableCell>
                      <TableCell align="right">自用面积(㎡)</TableCell>
                      <TableCell align="right">租赁面积(㎡)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockData.companies[0].assets.lands.map((land, index) => (
                      <TableRow key={index}>
                        <TableCell>{land.name}</TableCell>
                        <TableCell>{land.type}</TableCell>
                        <TableCell align="right">{land.area.toLocaleString()}</TableCell>
                        <TableCell align="right">
                          {(land.purchasePrice / 10000).toLocaleString()}
                        </TableCell>
                        <TableCell align="right">{land.selfUseArea.toLocaleString()}</TableCell>
                        <TableCell align="right">{land.rentalArea.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default AssetOverview;
