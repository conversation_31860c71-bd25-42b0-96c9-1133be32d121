import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  CardActions,
  Button,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { styles } from '../styles';
import { renderStatusChip } from '../utils/helpers';

const GroupAffairsCard = ({ title, icon, items, module, onAddClick }) => (
  <Card sx={styles.card}>
    <CardContent sx={styles.cardContent}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        {icon}
        <Typography variant="h6" sx={{ ml: 1 }}>
          {title}
        </Typography>
      </Box>
      <List>
        {}
        {items.map(item => (
          <ListItem key={item.id} sx={styles.listItem}>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {item.title}
                  {renderStatusChip(item.status)}
                </Box>
              }
              secondary={
                <>
                  <Typography component="span" variant="body2" color="text.primary">
                    {item.company} | {item.date}
                  </Typography>
                  <br />
                  {item.description}
                </>
              }
            />
          </ListItem>
        ))}
      </List>
    </CardContent>
    <CardActions>
      <Button startIcon={<AddIcon />} onClick={() => onAddClick(module)} color="primary">
        新增
      </Button>
    </CardActions>
  </Card>
);

GroupAffairsCard.propTypes = {
  title: PropTypes.string.isRequired,
  icon: PropTypes.element.isRequired,
  items: PropTypes.array.isRequired,
  module: PropTypes.string.isRequired,
  onAddClick: PropTypes.func,
};

export default GroupAffairsCard;
