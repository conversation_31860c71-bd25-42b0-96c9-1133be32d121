import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from '@mui/material';

// eslint-disable-next-line react/prop-types
const AddItemDialog = ({ open, onClose, selectedModule, formData, onFormChange, onSubmit }) => {
  const getModuleTitle = () => {
    switch (selectedModule) {
    case 'assetReporting':
      return '资产报备';
    case 'evaluationRecords':
      return '评估备案';
    case 'propertyRegistration':
      return '产权登记';
    case 'reviewCommittee':
      return '资产评审';
    default:
      return '';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>新增{getModuleTitle()}事项</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="标题"
          fullWidth
          // eslint-disable-next-line react/prop-types
          value={formData.title}
          onChange={e => onFormChange({ ...formData, title: e.target.value })}
        />
        <TextField
          margin="dense"
          label="公司"
          fullWidth
          // eslint-disable-next-line react/prop-types
          value={formData.company}
          onChange={e => onFormChange({ ...formData, company: e.target.value })}
        />
        <TextField
          margin="dense"
          label="描述"
          fullWidth
          multiline
          rows={4}
          // eslint-disable-next-line react/prop-types
          value={formData.description}
          onChange={e => onFormChange({ ...formData, description: e.target.value })}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>取消</Button>
        <Button onClick={onSubmit} variant="contained" color="primary">
          提交
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddItemDialog;
