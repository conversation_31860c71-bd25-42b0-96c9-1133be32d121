import React from 'react';
import { Grid, Card, CardContent, Typography, Box, CircularProgress } from '@mui/material';
import { styles } from '../styles';

// eslint-disable-next-line react/prop-types
const AssetStatistics = ({ assets, loading, apiConnected }) => {
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>正在加载统计数据...</Typography>
      </Box>
    );
  }

  if (!assets) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography color="text.secondary">暂无统计数据</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {assets.totalArea ? assets.totalArea.toLocaleString() : '0'}㎡
            </Typography>
            <Typography sx={styles.statLabel}>
              总资产面积
              {apiConnected && (
                <Typography variant="caption" color="success.main" sx={{ display: 'block' }}>
                  (实时数据)
                </Typography>
              )}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              ¥{assets.totalValue ? (assets.totalValue / 10000).toLocaleString() : '0'}万
            </Typography>
            <Typography sx={styles.statLabel}>
              总资产价值
              {apiConnected && (
                <Typography variant="caption" color="success.main" sx={{ display: 'block' }}>
                  (实时数据)
                </Typography>
              )}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {assets.selfUseArea ? assets.selfUseArea.toLocaleString() : '0'}㎡
            </Typography>
            <Typography sx={styles.statLabel}>
              自用面积
              {apiConnected && (
                <Typography variant="caption" color="success.main" sx={{ display: 'block' }}>
                  (实时数据)
                </Typography>
              )}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card sx={styles.card}>
          <CardContent sx={styles.cardContent}>
            <Typography variant="h6" sx={styles.statValue}>
              {assets.rentalArea ? assets.rentalArea.toLocaleString() : '0'}㎡
            </Typography>
            <Typography sx={styles.statLabel}>
              租赁面积
              {apiConnected && (
                <Typography variant="caption" color="success.main" sx={{ display: 'block' }}>
                  (实时数据)
                </Typography>
              )}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default AssetStatistics;
