import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
} from '@mui/material';

/**
 * 债权明细表组件
 * @param {string} title - 表格名称
 * @param {Array} data - 需要展示的数据（对象数组）
 * @param {string} sortBy - 需要降序排序的列名
 * @param {Array} columnOrder - 确保列显示顺序
 */
const PaginatedTable = ({ title, data, sortBy, columnOrder }) => {
  const [page, setPage] = useState(0); // 当前页数
  const rowsPerPage = 10; // 每页 10 行

  // noinspection NonAsciiCharacters
  const fixedColumnWidths = {
    管理公司: '5em', // 相当于约 5 个汉字宽度
    期间: '8em', // 相当于约 8 个汉字宽度
    是否涉诉: '3em', // 相当于约 2 个汉字宽度；若只需 1 个字符可改为 '1em'
    新增金额: '4em', // 相当于约 4 个汉字宽度
    减少金额: '4em', // 相当于约 4 个汉字宽度
    债权余额: '4em', // 相当于约 4 个汉字宽度
    债权金额: '4em', // 相当于约 4 个汉字宽度
    累计减少金额: '4em', // 相当于约 4 个汉字宽度
  };

  // 格式化数字列为最多2个数字小数
  const formatNumber = value => {
    if (typeof value === 'number') {
      return value.toFixed(2); // 保留最多2位小数
    }
    return value; // 返回原值，非数字列
  };

  // 按照指定列 `sortBy` 降序排序数据
  const sortedData = useMemo(() => {
    return [...data].sort((a, b) => (b[sortBy] || 0) - (a[sortBy] || 0));
  }, [data, sortBy]);

  // 处理分页
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden', marginBottom: '30px' }}>
      <h2 style={{ textAlign: 'center', padding: '12px', fontSize: '34px' }}>{title}</h2>
      <TableContainer style={{ width: '100%', overflowX: 'auto' }}>
        <Table
          style={{
            width: '100%',
            tableLayout: 'fixed', // 强制固定布局
            minWidth: 'max-content', // 保证最小宽度
          }}
          stickyHeader
        >
          {/* 使用 colgroup 控制列宽 */}
          <colgroup>
            {columnOrder.map(key => {
              const isFixed = !!fixedColumnWidths[key];
              return (
                <col
                  key={key}
                  style={{
                    // 如果是固定列则使用 fixedColumnWidths，否则列宽跟随表格内容自动调整
                    width: isFixed ? fixedColumnWidths[key] : 'auto',
                    // 对于固定列，不设置 minWidth，其他列可以设置最小宽度（例如 120px）
                    minWidth: isFixed ? undefined : '120px',
                  }}
                />
              );
            })}
          </colgroup>

          <TableHead>
            <TableRow>
              {columnOrder.map(key => (
                <TableCell
                  key={key}
                  sx={{
                    textAlign: 'center',
                    padding: '10px',
                    whiteSpace: 'nowrap',
                    fontSize: {
                      xs: '10px', // 手机端
                      md: '12px', // 桌面端
                    },
                    // 移除 width 相关设置，避免与 colgroup 冲突
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {key}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          <TableBody>
            {/* 空数据占位处理：保持表格结构，以免无数据时表格高度塌陷 */}
            {sortedData.length === 0 && (
              <TableRow style={{ visibility: 'hidden' }}>
                <TableCell
                  colSpan={columnOrder.length}
                  sx={{ padding: '10px', height: 50 }} // 保持最小高度
                >
                  Placeholder
                </TableCell>
              </TableRow>
            )}

            {sortedData.slice(page * rowsPerPage, (page + 1) * rowsPerPage).map(row => (
              <TableRow key={`${row.管理公司}-${row.债权人}-${row.债务人}`}>
                {columnOrder.map(colKey => (
                  <TableCell
                    key={colKey}
                    sx={{
                      textAlign: typeof row[colKey] === 'number' ? 'right' : 'center', // 数字列右对齐，其他列居中
                      padding: '10px',
                      fontSize: '12px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {formatNumber(row[colKey])} {/* 格式化数字 */}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* 分页控件 */}
      <TablePagination
        rowsPerPageOptions={[10]} // 每页显示10行
        component="div"
        count={data.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
      />
    </Paper>
  );
};

// 参数类型检查
PaginatedTable.propTypes = {
  title: PropTypes.string,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  sortBy: PropTypes.string.isRequired,
  columnOrder: PropTypes.arrayOf(PropTypes.string).isRequired, // 显示列的顺序
};

export default PaginatedTable;
