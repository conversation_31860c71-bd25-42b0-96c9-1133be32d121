// 引入必要的React库和组件
import React, { useState, useContext } from 'react';
import { Button, Snackbar } from '@mui/material'; // Material UI组件用于UI展示
import axios from 'axios'; // 用于发送HTTP请求的库
import PropTypes from 'prop-types'; // 类型检查库
import { AuthContext } from '../../../context/AuthContext'; // 引入认证上下文，包含用户token

/**
 * 下载Excel按钮组件
 *
 * @param {string} type - 下载类型，指定要下载的报表类型
 *                        'NewDebtDetails' - 新增债权明细表
 *                        'ReductionDebtDetails' - 处置债权明细表
 * @param {string} buttonText - 自定义按钮文本，可选
 * @param {string} year - 年份筛选条件，可选
 * @param {string} month - 月份筛选条件，可选
 * @param {string} company - 公司筛选条件，可选
 */
const DownloadExcelButton = ({ type, buttonText, year, month, company }) => {
  // 状态管理：跟踪下载状态和错误消息
  const [isDownloading, setIsDownloading] = useState(false); // 控制按钮禁用状态
  const [errorMessage, setErrorMessage] = useState(''); // 存储错误消息

  // 从AuthContext获取用户认证token
  const { token } = useContext(AuthContext);

  /**
   * 处理下载按钮点击事件
   * 发送API请求，获取Excel数据并触发浏览器下载
   */
  const handleDownload = async () => {
    // 防止用户重复点击
    if (isDownloading) {
      return;
    }
    // 设置下载中状态，禁用按钮
    setIsDownloading(true);

    try {
      // 检查用户是否已登录（token是否存在）
      if (!token) {
        throw new Error('用户未登录或会话已过期');
      }

      // 构建URL参数
      let url = `/api/export/${type}`;
      const params = new URLSearchParams();

      // 添加筛选参数
      if (year) {
        params.append('year', year);
      }
      if (month) {
        params.append('month', month);
      }
      if (company) {
        params.append('company', company);
      }

      // 如果有参数，添加到URL
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      // 发送API请求获取Excel文件

      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        headers: {
          // 添加认证头，使后端能识别当前用户
          Authorization: `Bearer ${token}`,
          Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      });

      // 检查响应状态
      if (response.status !== 200) {
        throw new Error(`服务器返回错误状态码: ${response.status}`);
      }

      // 检查响应是否包含数据
      if (!response.data || response.data.byteLength === 0) {
        throw new Error('下载的文件为空');
      }

      // 检查内容类型是否为错误响应
      const contentType = response.headers['content-type'];
      if (contentType?.includes('application/json')) {
        // 尝试解析JSON错误信息
        const decoder = new TextDecoder('utf-8');
        const jsonStr = decoder.decode(response.data);
        try {
          const errorObj = JSON.parse(jsonStr);
          throw new Error(`服务器返回错误: ${errorObj.message || JSON.stringify(errorObj)}`);
        } catch (e) {
          throw new Error('服务器返回了无法解析的JSON错误');
        }
      }

      // 将二进制数据创建为Blob对象

      const blob = new Blob([response.data], {
        // 指定MIME类型为Excel文件
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      // 创建一个临时的<a>元素来触发下载
      const link = document.createElement('a');
      // 使用URL.createObjectURL创建一个指向blob的URL
      link.href = URL.createObjectURL(blob);

      // 根据下载类型设置合适的文件名
      const fileName = type === 'NewDebtDetails' ? '新增债权明细表.xlsx' : '处置债权明细表.xlsx';
      link.download = fileName;

      // 将链接添加到DOM中（不可见）
      document.body.appendChild(link);
      // 模拟点击触发下载
      link.click();
      // 从DOM中移除链接
      document.body.removeChild(link);

      // 释放URL对象，避免内存泄漏
      URL.revokeObjectURL(link.href);
    } catch (error) {
      // 记录详细错误到控制台，便于调试
      console.error('下载文件时出错:', error);
      // 提取更详细的错误信息
      let errorMsg = error.message || '下载失败，请检查网络连接并重试';
      if (error.response) {
        errorMsg += ` (状态码: ${error.response.status})`;
        console.error('错误响应:', error.response);
      }
      // 向用户显示友好的错误消息
      setErrorMessage(errorMsg);
    } finally {
      // 无论成功或失败，都重置下载状态
      setIsDownloading(false);
    }
  };

  // 组件渲染内容
  return (
    <div>
      {/* 下载按钮 */}
      <Button
        variant="contained"
        color="primary"
        onClick={handleDownload}
        disabled={isDownloading}
        sx={{
          backgroundColor: theme => theme.palette.grey[500],
          '&:hover': {
            backgroundColor: theme => theme.palette.grey[700],
          },
          whiteSpace: 'nowrap', // 防止文本换行
          padding: '6px 12px', // 调整内边距
          minWidth: 'auto', // 允许按钮宽度自适应
          fontSize: '0.875rem', // 设置字体大小
          height: '32px', // 固定高度
        }}
      >
        {isDownloading ? '正在下载...' : buttonText || '下载'}
      </Button>

      {/* 错误提示消息组件 */}
      <Snackbar
        open={Boolean(errorMessage)} // 当有错误消息时显示
        autoHideDuration={5000} // 5秒后自动隐藏
        onClose={() => setErrorMessage('')} // 关闭时清空错误消息
        message={errorMessage} // 显示的错误文本
      />
    </div>
  );
};

// 组件属性类型检查
DownloadExcelButton.propTypes = {
  // type属性必须是指定的两个字符串之一
  type: PropTypes.oneOf(['NewDebtDetails', 'ReductionDebtDetails']).isRequired,
  buttonText: PropTypes.string, // 按钮文本属性的类型检查
  year: PropTypes.string, // 年份筛选条件
  month: PropTypes.string, // 月份筛选条件
  company: PropTypes.string, // 公司筛选条件
};

// 导出组件供其他文件使用
export default DownloadExcelButton;
