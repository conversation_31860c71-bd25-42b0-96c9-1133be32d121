import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Legend, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent } from './ui/card';
import PropTypes from 'prop-types';

// 自定义标签渲染函数
const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
  const RADIAN = Math.PI / 180;
  const radius = outerRadius * 0.6;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="black"
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      fontSize="16"
      fontWeight="lighter"
    >
      {`${(percent * 100).toFixed(1)}%`}
    </text>
  );
};

/** 自定义 Tooltip 组件 */
const CustomTooltip = ({ active, payload, coordinate }) => {
  if (active && payload?.[0]) {
    const { x, y } = coordinate;

    return (
      <div
        style={{
          position: 'absolute',
          top: `${y}px`,
          left: `${x}px`,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          padding: '5px 10px',
          borderRadius: '4px',
          color: payload[0].fill,
          fontSize: '12px',
          fontWeight: 'lighter',
          transform: 'translate(-50%, -50%)',
          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
          border: '1px solid #f0f0f0',
          minWidth: '75px',
          minHeight: '40px',
        }}
      >
        <span style={{ display: 'block' }}>
          {payload[0].name}: {payload[0].value}
        </span>
      </div>
    );
  }
  return null;
};

const CustomPieChart = ({ title, data, summaryText }) => {
  return (
    <Card
      className="p-4 shadow-lg rounded-lg"
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* 标题部分 - 左上角 */}
      <div className="flex justify-center items-center mb-2" style={{ width: '100%' }}>
        <h2 className="text-sm font-semibold" style={{ fontSize: '17px' }}>
          {title}
        </h2>
      </div>

      {/* 饼图部分 - 中间显示 */}
      <CardContent className="flex-grow flex justify-center items-center p-0">
        <ResponsiveContainer width="100%" height={280} aspect={1}>
          <PieChart margin={{ top: 10, right: 10, bottom: 10, left: 10 }}>
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius="100%"
              innerRadius="0%"
              paddingAngle={0}
              labelLine={false}
              label={renderCustomLabel}
            >
              {data.map(entry => (
                <Cell key={`cell-${entry.name}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend
              verticalAlign="bottom"
              align="center"
              iconType="circle"
              layout="horizontal"
              wrapperStyle={{
                fontSize: '12px',
                paddingTop: '10px',
                paddingBottom: '5px',
                fontWeight: 'bold',
              }}
            />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>

      {/* 底部信息区域 - 只显示摘要文本 */}
      {summaryText && (
        <div style={{ marginTop: 'auto' }}>
          <p
            className="text-gray-700"
            style={{
              fontSize: '12px',
            }}
          >
            {summaryText}
          </p>
        </div>
      )}
    </Card>
  );
};

// 添加 PropTypes 校验
CustomTooltip.propTypes = {
  active: PropTypes.bool,
  payload: PropTypes.arrayOf(
    PropTypes.shape({
      fill: PropTypes.string,
      name: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  ),
  coordinate: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
  }).isRequired,
};

// 🔹 添加 PropTypes 校验
CustomPieChart.propTypes = {
  title: PropTypes.string.isRequired, // title 是必传的
  data: PropTypes.array.isRequired, // data 是必传的
  summaryText: PropTypes.string, // summaryText 是可选的
};

// 如果 summaryText 没有传递，使用空字符串作为默认值
CustomPieChart.defaultProps = {
  summaryText: '', // 默认值为空字符串
};

export default CustomPieChart;
