import React, { useState } from 'react';
import { Select, MenuItem, FormControl, InputLabel, Button, Box } from '@mui/material';
import PropTypes from 'prop-types'; // 引入 PropTypes
import { useAuth } from 'context/AuthContext'; // 导入 useAuth hook

// 管理公司、年份、月份筛选条件
const OverdueStatisticsFilter = ({ onSearch }) => {
  const { user } = useAuth(); // 获取当前用户信息
  const currentYear = '2025年'; // 默认选择最新的年份
  const currentMonth = `${new Date().getMonth() + 1}月`; // 获取当前月份，默认选中

  // 根据用户权限和公司设置默认公司
  const defaultCompany = user?.role === 'ROLE_ADMIN' ? '所有公司' : user?.company;

  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);
  const [selectedCompany, setSelectedCompany] = useState(defaultCompany);

  // Options for each select dropdown
  // 改成年2024年和2025年
  const years = ['2024年', '2025年'];
  const months = Array.from({ length: 12 }, (_, i) => `${i + 1}月`); // ["1月", "2月", ..., "12月"]
  const companies = [
    '所有公司',
    '公司总部',
    '亿万无线',
    '信立传媒',
    '恒润光电',
    '万象新动',
    '长春万润',
    '中筑天佑',
    '日上光电',
    '湖北万润',
    '长江万润',
    '万润半导体',
    '万润新能源',
  ];

  // Handle the search button click
  // 处理筛选按钮点击事件
  const handleSearch = () => {
    onSearch(selectedYear, selectedMonth, selectedCompany);
  };

  // 判断是否禁用公司选择
  const isCompanySelectDisabled = user?.role !== 'ROLE_ADMIN';

  // 获取可选的公司列表
  const getAvailableCompanies = () => {
    if (user?.role === 'ROLE_ADMIN') {
      return companies;
    }
    return [user?.company];
  };

  return (
    <Box
      sx={{
        position: 'relative',
        top: '0.5rem', // 调整距离顶部的距离
        left: '1rem', // 调整距离左侧的距离
        zIndex: 99,
        display: 'flex',
        flexWrap: 'wrap', // 添加换行处理
        gap: '15px', // 控制选择框之间的间隙
        justifyContent: 'center',
        alignItems: 'center',
        maxWidth: '100%', // 限制最大宽度
      }}
    >
      {/* Year Selector */}
      <FormControl sx={{ minWidth: 60 }}>
        <InputLabel>年份</InputLabel>
        <Select
          value={selectedYear}
          onChange={e => setSelectedYear(e.target.value)}
          label="年份"
          MenuProps={{
            PaperProps: {
              style: {
                width: '40px', // 设置下拉菜单的宽度
              },
            },
          }}
        >
          {years.map(year => (
            <MenuItem
              key={year}
              value={year}
              sx={{
                paddingLeft: '2px', // 调整左右内边距
                paddingRight: '2px',
              }}
            >
              {year}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Month Selector */}
      <FormControl sx={{ minWidth: 60 }}>
        <InputLabel>月份</InputLabel>
        <Select
          value={selectedMonth}
          onChange={e => setSelectedMonth(e.target.value)}
          label="月份"
          MenuProps={{
            PaperProps: {
              style: {
                width: '60px', // 设置下拉菜单的宽度
              },
            },
          }}
        >
          {months.map(month => (
            <MenuItem
              key={month}
              value={month}
              sx={{
                paddingLeft: '2px', // 调整左右内边距
                paddingRight: '2px',
              }}
            >
              {month}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Company Selector */}
      <FormControl sx={{ minWidth: 100 }}>
        <InputLabel>管理公司</InputLabel>
        <Select
          value={selectedCompany}
          onChange={e => setSelectedCompany(e.target.value)}
          label="管理公司"
          disabled={isCompanySelectDisabled}
          MenuProps={{
            PaperProps: {
              style: {
                width: '100px', // 设置下拉菜单的宽度
              },
            },
          }}
        >
          {getAvailableCompanies().map(company => (
            <MenuItem
              key={company}
              value={company}
              sx={{
                paddingLeft: '2px', // 调整左右内边距
                paddingRight: '2px',
              }}
            >
              {company}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Search Button */}
      <Button
        variant="contained"
        onClick={handleSearch}
        sx={{
          backgroundColor: '#F5F5F5', // 现代简洁风：浅灰色背景
          color: '#333333', // 深灰色文字，增强可读性
          fontWeight: 'bold', // 字体加粗
          borderRadius: '8px', // 圆角风格
          boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)', // 轻微阴影
          border: '1px solid #D3D3D3', // 添加淡灰色边框
          '&:hover': {
            backgroundColor: '#E0E0E0', // 悬停时变为更深的灰色
            border: '1px solid #A0A0A0', // 悬停时边框加深
          },
          minWidth: '80px', // 按钮宽度，防止变形
          minHeight: '36px', // 按钮高度，符合标准尺寸
          padding: '6px 16px', // 适当内边距，让按钮更美观
          textTransform: 'none', // 禁用大写，让文本更自然
        }}
      >
        筛选
      </Button>
    </Box>
  );
};
// 添加 propTypes 验证
OverdueStatisticsFilter.propTypes = {
  onSearch: PropTypes.func.isRequired, // 验证 onSearch 属性为函数类型，且是必需的
};

export default OverdueStatisticsFilter;
