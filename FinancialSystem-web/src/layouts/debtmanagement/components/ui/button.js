import React from 'react';
import PropTypes from 'prop-types';

export const Button = ({ children, onClick, className, disabled, loading }) => (
  <button
    onClick={onClick}
    className={`px-4 py-2 rounded-md font-medium transition-all ${
      loading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'
    } ${className}`}
    disabled={disabled || loading}
  >
    {loading ? 'Loading...' : children}
  </button>
);

// 🔹 添加 PropTypes
Button.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
};

// 默认值
Button.defaultProps = {
  onClick: () => {}, // 防止点击时调用未定义的函数
  className: '',
  disabled: false,
  loading: false,
};

export default Button;
