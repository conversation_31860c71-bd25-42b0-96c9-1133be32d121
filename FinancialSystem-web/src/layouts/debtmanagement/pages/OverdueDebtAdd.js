import React, { useEffect, useState } from 'react';
import api from '../../../utils/api';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
} from '@mui/material';
import GenericDataTable from 'components/tables/GenericDataTable';
import FormInput from '../components/inputform/FormInput';
import FormSelect from '../components/inputform/FormSelect';
import { FormMonthPicker, FormDatePicker } from '../../../components/forms';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import DownloadExcelButton from '../components/DownloadExcelButton';
import { useAuth } from '../../../context/AuthContext';

// 定义主题和样式

const styles = {
  searchResults: {
    marginTop: 1.5,
    marginBottom: 1.5,
    padding: 1.5,
    borderRadius: 1,
    boxShadow: '0 2px 10px 0 rgba(0,0,0,0.08)',
    backgroundColor: '#f9f9f9',
  },
  resultsTitle: {
    fontSize: '15px',
    fontWeight: 500,
    color: '#3f51b5',
    marginBottom: 0.8,
  },
  noResults: {
    textAlign: 'center',
    padding: 1.5,
    color: '#757575',
    fontSize: '13px',
  },
  root: {
    '& .MuiTextField-root': {
      marginBottom: 1.5,
    },
  },
  paper: {
    padding: 2,
    margin: '10px 0',
    borderRadius: 1,
    boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)',
    backgroundColor: '#ffffff',
  },
  title: {
    fontSize: '18px',
    fontWeight: 600,
    color: '#1a237e',
    marginBottom: 1.5,
    textAlign: 'center',
  },
  section: {
    marginTop: 1.5,
  },
  sectionTitle: {
    fontSize: '15px',
    fontWeight: 500,
    color: '#283593',
    marginBottom: 1,
  },
  formGroup: {
    marginBottom: 1,
  },
  submitButton: {
    marginTop: 1.5,
    padding: '8px 0',
    backgroundColor: '#1976d2', // 与页面其他蓝色元素保持一致
    color: '#ffffff', // 白色文字，增强对比度
    fontWeight: 500, // 加粗文字
    '&:hover': {
      backgroundColor: '#1565c0',
    },
    fontSize: '13px',
    height: '36px',
    boxShadow: 'none', // 移除阴影，使其更简约
    borderRadius: '4px',
    textTransform: 'none', // 不将文本转为大写
  },
  resetButton: {
    marginTop: 1.5,
    padding: '8px 0',
    backgroundColor: '#f5f5f5', // 浅灰色背景
    color: '#333', // 深色文字
    '&:hover': {
      backgroundColor: '#e0e0e0',
    },
    fontSize: '13px',
    height: '36px',
    boxShadow: 'none', // 移除阴影
    borderRadius: '4px',
    textTransform: 'none', // 不将文本转为大写
  },
  input: {
    fontSize: '13px',
  },
  managementCompanySelect: {
    maxWidth: '100%',
    '& .MuiList-root': {
      padding: '0',
    },
    '& .MuiMenuItem-root': {
      minHeight: '40px',
      padding: '8px 16px',
      fontSize: '14px',
      justifyContent: 'flex-start',
      '&.Mui-selected': {
        backgroundColor: '#f5f9ff',
      },
      '&.Mui-selected:hover': {
        backgroundColor: '#f5f9ff',
      },
      '&:hover': {
        backgroundColor: '#f5f5f5',
      },
    },
  },
};

const OverdueDebtAdd = () => {
  const { user } = useAuth(); // 获取当前用户信息

  // 判断用户是否是管理员
  const isAdmin = user?.role === 'ADMIN' || user?.role === 'ROLE_ADMIN';
  // 获取当前日期
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();

  const yearMonthOptions = [];
  for (let y = 2025; y <= 2026; y++) {
    for (let m = 1; m <= 12; m++) {
      const mStr = m.toString().padStart(2, '0');
      yearMonthOptions.push({
        value: `${y}-${mStr}`,
        label: `${y}年${m}月`,
      });
    }
  }

  // 获取当前系统年月作为默认值
  const currentYearMonth = `${currentDate.getFullYear()}-${(currentDate.getMonth() + 1)
    .toString()
    .padStart(2, '0')}`; // 月份从0开始，需要+1
  const defaultYearMonth = currentYearMonth;

  // 计算默认月份的最后一天
  const calcLastDayOfMonth = yearMonth => {
    const [year, month] = yearMonth.split('-').map(num => parseInt(num, 10));
    const lastDay = new Date(year, month, 0).getDate();
    return `${yearMonth}-${String(lastDay).padStart(2, '0')}`;
  };

  // 初始化默认逾期日期为默认月份的最后一天
  const defaultLastDayOfMonth = calcLastDayOfMonth(defaultYearMonth);

  // 初始化表单状态
  const [formData, setFormData] = useState({
    creditor: '',
    debtor: '',
    managementCompany: isAdmin ? '' : user?.company || user?.companyname || '',
    isLitigation: '否',
    caseName: '',
    subjectName: '应收账款', // 默认为"应收账款"
    overdueAmount: '',
    overdueDate: defaultLastDayOfMonth, // 默认为逾期所属期间月份的最后一天
    provisionAmount: '0', // 默认为0
    responsiblePerson: '',
    debtCategory: '生产经营类债权',
    debtNature: '生产经营类债权',
    litigationClaim: {
      principal: 0,
      interest: 0,
      penalty: 0,
      litigationFee: 0,
      intermediaryFee: 0,
    },
    measures: '',
    addDate: defaultYearMonth,
  });

  const [errors, setErrors] = useState({});
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedRows, setSelectedRows] = useState({});

  // 添加输入警告状态
  const [inputWarnings, setInputWarnings] = useState({});

  // 当年新增债权记录状态
  const [addRecords, setAddRecords] = useState([]);
  const [isLoadingAddRecords, setIsLoadingAddRecords] = useState(false);
  const [deletingRecordIds, setDeletingRecordIds] = useState({}); // 使用对象跟踪正在删除的记录ID

  // 新增状态：跟踪字段是否只读
  const [fieldsReadOnly, setFieldsReadOnly] = useState({
    managementCompany: false,
    isLitigation: false, // 改为布尔值，表示是否只读
  });

  // 新增：跟踪是否处于更新模式
  const [isUpdateMode, setIsUpdateMode] = useState(false);

  // 常用债权人列表用于自动完成
  const managementCompanies = [
    '万润科技',
    '日上光电',
    '恒润光电',
    '信立传媒',
    '万润半导体',
    '中筑天佑',
    '亿万无线',
    '万润新能源',
    '湖北万润',
    '长江万润',
    '长春万润',
    '万象新动',
  ];

  // 通过计算得到的变量，在UI中条件判断时使用
  // 只有当贸易类债权时，才需要填写被担保人

  const debtCategoryOptions = [
    { value: '生产经营类债权', label: '生产经营类债权' },
    { value: '贸易类债权', label: '贸易类债权' },
    { value: '平台类债权', label: '平台类债权' },
    { value: '金融类债权', label: '金融类债权' },
    { value: '其他类债权', label: '其他类债权' },
  ];

  const debtNatureOptions = [
    { value: '生产经营类债权', label: '生产经营类债权' },
    { value: '贸易类债权', label: '贸易类债权' },
    { value: '平台类债权', label: '平台类债权' },
    { value: '金融类债权', label: '金融类债权' },
    { value: '其他类债权', label: '其他类债权' },
  ];

  // 表单验证
  const validate = () => {
    const newErrors = {};
    if (!formData.creditor) {
      newErrors.creditor = '请输入债权人';
    }
    if (!formData.debtor) {
      newErrors.debtor = '请输入债务人';
    }
    if (!formData.overdueAmount) {
      newErrors.overdueAmount = '请输入新增逾期金额';
    } else if (isNaN(formData.overdueAmount)) {
      newErrors.overdueAmount = '新增逾期金额必须是数字';
    }
    return newErrors;
  };

  const handleSubmit = async e => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // 检查是否有警告，但允许继续提交
    if (Object.keys(inputWarnings).some(key => inputWarnings[key])) {
      const confirmSubmit = window.confirm(
        '输入中存在小数位警告，提交后将自动四舍五入保留两位小数。是否继续？',
      );
      if (!confirmSubmit) {
        return;
      }
    }

    // 检查是否需要提示用户使用更新模式
    // 只在不是更新模式时进行检查
    if (!isUpdateMode && searchResults.length > 0) {
      // 从表单中提取归属期间
      const formPeriod = formData.addDate;

      // 提取表单中归属期间的年份
      const formYearString = formPeriod.split('-')[0];

      // 查找相匹配的记录：债权人、债务人、年份、管理公司和是否涉诉完全一致
      const matchingRecord = searchResults.find(record => {
        // 提取搜索结果中归属期间的年份 - 支持多种格式
        let recordYearString = '';
        if (record.period) {
          if (record.period.includes('-')) {
            recordYearString = record.period.split('-')[0];
          } else if (record.period.includes('年')) {
            recordYearString = record.period.split('年')[0];
          }
        }

        // 检查关键字段匹配 - 比较债权人、债务人、年份、管理公司和是否涉诉
        const creditorMatch = record.creditor === formData.creditor;
        const debtorMatch = record.debtor === formData.debtor;
        const yearMatch = recordYearString === formYearString;

        // 新增管理公司和是否涉诉的匹配检查
        const managementCompanyMatch = record.managementCompany === formData.managementCompany;
        const isLitigationMatch = record.isLitigation === formData.isLitigation;

        // 所有字段都匹配才返回true
        const allFieldsMatch =
          creditorMatch && debtorMatch && yearMatch && managementCompanyMatch && isLitigationMatch;

        // 打印较详细的匹配信息用于调试
        if (creditorMatch && debtorMatch) {
          console.log(
            '年份匹配:',
            yearMatch,
            'record年份:',
            recordYearString,
            'form年份:',
            formYearString,
          );
          console.log(
            '管理公司匹配:',
            managementCompanyMatch,
            'record管理公司:',
            record.managementCompany,
            'form管理公司:',
            formData.managementCompany,
          );
          console.log(
            '是否涉诉匹配:',
            isLitigationMatch,
            'record是否涉诉:',
            record.isLitigation,
            'form是否涉诉:',
            formData.isLitigation,
          );
        }

        return allFieldsMatch; // 只有当所有字段都匹配时才返回true
      });

      if (matchingRecord) {
        // 构建更详细的匹配信息
        const matchInfo = `债权人: ${matchingRecord.creditor}\n债务人: ${
          matchingRecord.debtor
        }\n期间: ${matchingRecord.period || ''}\n管理公司: ${
          matchingRecord.managementCompany || ''
        }\n是否涉诉: ${matchingRecord.isLitigation || ''}`;

        const confirmUpdate = window.confirm(
          `发现完全相同的记录！该债权人、债务人、年份、管理公司和是否涉诉完全一致的记录已存在。\n\n匹配记录详情：\n${matchInfo}\n\n请在上方搜索结果中勾选该记录进行更新。\n\n点击确定取消提交，点击取消则尝试继续提交（不推荐）`,
        );
        if (confirmUpdate) {
          return; // 用户选择取消提交
        }

        // 再次确认是否继续
        const secondConfirm = window.confirm(
          '创建完全相同的记录可能导致数据重复，建议勾选现有记录进行更新。确定要继续提交吗？',
        );
        if (!secondConfirm) {
          return; // 用户取消提交
        }
        // 否则继续提交表单
      }
    }

    // 计算诉讼主张总金额 - 将对象转为数字
    // 将所有值转为数字并相加
    const calculateTotalClaim = () => {
      const { principal, interest, penalty, litigationFee, intermediaryFee } =
        formData.litigationClaim;
      const principalNum = parseFloat(principal) || 0;
      const interestNum = parseFloat(interest) || 0;
      const penaltyNum = parseFloat(penalty) || 0;
      const litigationFeeNum = parseFloat(litigationFee) || 0;
      const intermediaryFeeNum = parseFloat(intermediaryFee) || 0;

      return principalNum + interestNum + penaltyNum + litigationFeeNum + intermediaryFeeNum;
    };

    // 在提交表单时加入更新模式标记
    // 准备提交数据，将litigationClaim转换为数字

    // 处理period字段，根据是否勾选更新行来决定使用哪个值
    let period;

    // 如果是更新模式并且有选中的行
    if (isUpdateMode && Object.values(selectedRows).some(Boolean)) {
      // 找到选中的第一行数据
      const selectedRow = processedResults.find(item => selectedRows[item.id]);
      if (selectedRow && selectedRow.period) {
        // 使用选中行的归属期间
        period = selectedRow.period;
      } else {
        // 提取年份
        const [year] = formData.addDate.split('-');
        period = `${year}年新增债权`;
      }
    } else {
      // 如果没有勾选更新行，使用年月格式
      const [year] = formData.addDate.split('-');
      period = `${year}年新增债权`;
    }

    // 预处理数据，四舍五入所有金额到两位小数
    const prepareDataForSubmission = () => {
      const data = { ...formData };

      // 四舍五入金额字段
      if (data.overdueAmount) {
        data.overdueAmount = parseFloat(parseFloat(data.overdueAmount).toFixed(2));
      }
      if (data.provisionAmount) {
        data.provisionAmount = parseFloat(parseFloat(data.provisionAmount).toFixed(2));
      }

      // 处理诉讼主张相关金额
      if (data.litigationClaim) {
        Object.keys(data.litigationClaim).forEach(key => {
          if (data.litigationClaim[key]) {
            data.litigationClaim[key] = parseFloat(
              parseFloat(data.litigationClaim[key]).toFixed(2),
            );
          }
        });
      }

      return data;
    };

    const dataToSubmit = {
      ...prepareDataForSubmission(),
      litigationClaim: calculateTotalClaim(), // 将对象转换为总和数字（已四舍五入）
      updateMode: isUpdateMode, // 将更新模式状态传递给后端
      period: period, // 添加period字段
    };

    try {
      const response = await api.post('/debts/add', dataToSubmit);
      if (response.status === 200) {
        alert('提交成功');

        // 保存当前的债权人和债务人信息，用于刷新搜索结果
        const currentCreditor = formData.creditor;
        const currentDebtor = formData.debtor;

        resetForm();

        // 提交成功后刷新数据
        setTimeout(async () => {
          // 1. 刷新当年新增债权记录表
          await fetchCurrentYearAddRecords();

          // 2. 触发定时任务更新后续月份数据
          try {
            const taskResponse = await api.post('/tasks/trigger-debt-update');
            if (taskResponse.status === 200) {
            }
          } catch (taskError) {}

          // 3. 如果之前有搜索结果，重新触发搜索以更新余额
          if (currentCreditor && currentDebtor) {
            await autoSearch(currentCreditor, currentDebtor);
          }
        }, 1000);
      }
    } catch (error) {
      console.error('提交失败:', error);

      // 处理后端返回的错误消息
      let errorMessage = '提交失败';

      if (error.response) {
        if (error.response.status === 403) {
          errorMessage = '权限不足，请确保您已登录并有权限提交此表单';
        } else if (error.response?.data?.message) {
          // 如果错误消息中包含"已存在"相关内容，展示特定的提示
          if (
            error.response.data.message.includes('已存在') ||
            error.response.data.message.includes('勾选')
          ) {
            errorMessage = error.response.data.message;
            // 标记需要展示记录选择提示
            alert(errorMessage);
            return; // 返回而不重置表单，让用户可以选择记录
          } else {
            errorMessage = error.response.data.message;
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    }
  };

  const resetForm = () => {
    try {
      // 重新获取当前日期，确保使用最新的时间
      const now = new Date();

      // 计算当前年月
      const resetYearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

      // 计算当前月份的最后一天
      const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
      const resetLastDayOfMonth = `${resetYearMonth}-${String(lastDay).padStart(2, '0')}`;

      // 准备新的表单数据
      const newFormData = {
        creditor: '',
        debtor: '',
        managementCompany: isAdmin ? '' : user?.company || user?.companyname || '',
        isLitigation: '否',
        caseName: '',
        subjectName: '应收账款', // 设置默认科目名称
        overdueAmount: '',
        overdueDate: resetLastDayOfMonth, // 设置默认逾期日期
        provisionAmount: '0',
        responsiblePerson: '',
        debtCategory: '生产经营类债权',
        debtNature: '生产经营类债权',
        litigationClaim: {
          principal: 0,
          interest: 0,
          penalty: 0,
          litigationFee: 0,
          intermediaryFee: 0,
        },
        measures: '',
        addDate: resetYearMonth, // 设置默认逾期所属期间
      };

      // 更新表单状态
      setFormData(newFormData);

      // 重置其他状态
      setErrors({});
      setFieldsReadOnly({
        managementCompany: false,
        isLitigation: false,
      });
      setIsUpdateMode(false);
      setSelectedRows({});
    } catch (error) {
      console.error('重置表单时出错:', error);
    }
  };

  // 自动检索功能
  const autoSearch = async (creditor, debtor) => {
    if (!creditor && !debtor) {
      setSearchResults([]);
      setHasSearched(false);
      return;
    }

    setIsSearching(true);
    setHasSearched(true);

    try {
      const queryParams = new URLSearchParams();
      if (creditor) {
        queryParams.append('creditor', creditor);
      }
      if (debtor) {
        queryParams.append('debtor', debtor);
      }

      const response = await api.get(`/debts/adds/search?${queryParams.toString()}`);
      if (response.status === 200) {
        // 确保搜索结果中的字段正确
        const processedData = (response.data || []).map(item => ({
          ...item,
          isLitigation: item.isLitigation || '否', // 确保是否涉诉有默认值
          managementCompany: item.managementCompany || '', // 确保管理公司有默认值
        }));
        // 调试日志
        setSearchResults(processedData);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 格式化货币显示
  const formatCurrency = amount => {
    if (amount === null || amount === undefined) {
      return '-';
    }
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // 组件挂载时获取当年新增债权数据
  useEffect(() => {
    // 在组件加载时，获取当前年度的新增债权记录

    fetchCurrentYearAddRecords();

    // 空依赖数组确保只在组件挂载时执行一次
  }, []);

  // 添加余额更新事件监听
  useEffect(() => {
    const handleBalanceUpdate = async event => {
      const { creditor, debtor, action } = event.detail;

      // 如果更新的债权与当前显示的相关，则刷新数据
      if (
        (creditor === formData.creditor || debtor === formData.debtor) &&
        formData.creditor &&
        formData.debtor
      ) {
        // 强制刷新搜索结果以更新余额显示
        await autoSearch(formData.creditor, formData.debtor);
      }

      // 如果是删除操作，总是刷新新增记录列表
      if (action === 'delete') {
        await fetchCurrentYearAddRecords();
      }
    };

    // 注册事件监听器
    window.addEventListener('debtBalanceUpdated', handleBalanceUpdate);

    // 清理函数
    return () => {
      window.removeEventListener('debtBalanceUpdated', handleBalanceUpdate);
    };
  }, [formData.creditor, formData.debtor]);

  // 处理删除债权记录的函数
  const handleDeleteRecord = async record => {
    if (
      !window.confirm(
        `确定要删除该债权记录吗？\n\n债权人: ${record.creditor}\n债务人: ${record.debtor}\n月份: ${
          record.month
        }\n新增金额: ${parseFloat(record.debtAmount).toFixed(2)}万元`,
      )
    ) {
      return; // 用户取消删除
    }

    // 将当前记录标记为正在删除状态
    setDeletingRecordIds(prev => ({ ...prev, [record.id]: true }));

    try {
      // 获取当前年份
      const currentYear = new Date().getFullYear();

      // 格式化月份，确保是两位数
      let month = record.month;
      // 如果month是数字，转为字符串
      if (typeof month === 'number') {
        month = month.toString();
      }

      // 移除可能存在的年份部分，只保留月份数字
      const monthPart = month.includes('-') ? month.split('-')[1] : month;

      // 格式化为两位数月份，转换为数字
      const monthNumber = parseInt(monthPart);

      // 准备删除数据
      const deleteData = {
        creditor: record.creditor,
        debtor: record.debtor,
        managementCompany: record.managementCompany,
        isLitigation: record.isLitigation,
        period:
          record.period ||
          record.originalData?.period ||
          `${currentYear}-${monthPart.padStart(2, '0')}`,
        year: currentYear,
        month: monthNumber,
        amount: parseFloat(record.debtAmount),
        deleteReason: '用户手动删除新增债权记录',
        operatorName: '当前用户', // 实际应该从用户上下文获取
      };

      // 调用新的删除API接口
      const response = await api.post('/debt/deletion/addition', deleteData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200 && response.data.success) {
        alert(response.data.message || '删除成功');

        // 1. 重新获取新增记录数据
        await fetchCurrentYearAddRecords();

        // 2. 如果当前有搜索条件，重新触发搜索以更新余额显示
        // 这样可以确保查询结果表格中的余额数据也得到更新
        if (formData.creditor && formData.debtor) {
          await autoSearch(formData.creditor, formData.debtor);
        }

        // 3. 触发全局余额更新事件，通知其他可能需要更新的组件
        window.dispatchEvent(
          new CustomEvent('debtBalanceUpdated', {
            detail: {
              creditor: record.creditor,
              debtor: record.debtor,
              action: 'delete',
              amount: record.debtAmount,
            },
          }),
        );
      } else {
        alert(`删除失败: ${response.data?.message || response.statusText}`);
      }
    } catch (error) {
      console.error('删除过程中出错:', error);
      alert(`删除出错: ${error.message || '未知错误'}`);
    } finally {
      // 删除操作完成，取消该记录的删除状态
      setDeletingRecordIds(prev => {
        const newState = { ...prev };
        delete newState[record.id];
        return newState;
      });
    }
  };

  // 获取当年所有新增债权记录
  const fetchCurrentYearAddRecords = async () => {
    setIsLoadingAddRecords(true);

    try {
      // 仅获取当年所有新增债权记录
      const url = '/debts/adds/search-all';

      const response = await api.get(url);

      if (response.status === 200) {
        const records = response.data || [];

        // 处理数据，确保所有字段格式正确
        const processedRecords = records.map((record, index) => {
          return {
            id: index + 1,
            creditor: record.creditor || '',
            debtor: record.debtor || '',
            isLitigation: record.isLitigation || '否',
            month: record.addDate || record.yearMonth || record.month || '',
            debtAmount: record.currentMonthNewDebt || '0',
            managementCompany: record.managementCompany || '',
            period: record.period || '', // 保存期间字段
            // 保存原始数据引用
            originalData: record,
          };
        });

        setAddRecords(processedRecords);
      } else {
        console.error('获取当前年度新增债权记录失败:', response.statusText);
        setAddRecords([]);
      }
    } catch (error) {
      console.error('获取当前年度新增债权记录错误:', error);
      setAddRecords([]);
    } finally {
      setIsLoadingAddRecords(false);
    }
  };

  // 处理选择框点击
  const handleCheckboxClick = rowId => {
    const newSelectedState = !selectedRows[rowId];
    setSelectedRows(prev => ({
      ...prev,
      [rowId]: newSelectedState,
    }));
    // 根据是否选中，更新管理公司和是否涉诉的状态
    if (newSelectedState) {
      // 找到选中的行数据
      const selectedRow = processedResults.find(item => item.id === rowId);
      if (selectedRow) {
        // 更新表单数据：填充管理公司和是否涉诉
        // 调试日志
        setFormData(prev => ({
          ...prev,
          managementCompany: selectedRow.managementCompany || '',
          isLitigation: selectedRow.isLitigation || '否', // 确保有默认值
        }));

        // 设置字段为只读
        setFieldsReadOnly({
          managementCompany: true,
          isLitigation: true, // 改为布尔值true，表示字段只读
        });

        // 设置为更新模式
        setIsUpdateMode(true);
      }
    } else {
      // 如果没有任何行被选中，则重置只读状态
      const anyRowSelected = Object.values({ ...selectedRows, [rowId]: newSelectedState }).some(
        Boolean,
      );
      if (!anyRowSelected) {
        setFieldsReadOnly({
          managementCompany: false,
          isLitigation: false, // 改为布尔值false，表示字段不只读
        });
        setIsUpdateMode(false);
      }
    }
  };

  // 预处理数据，保证金额字段为数值
  const processedResults = searchResults.map((item, idx) => ({
    id: idx,
    creditor: item.creditor || '',
    debtor: item.debtor || '',
    period: item.period || '',
    isLitigation: item.isLitigation || '否',
    managementCompany: item.managementCompany || '',
    newAmount:
      typeof item.newAmount === 'number' ? item.newAmount : parseFloat(item.newAmount) || 0,
    debtBalance:
      typeof item.debtBalance === 'number' ? item.debtBalance : parseFloat(item.debtBalance) || 0,
    reductionAmount:
      typeof item.reductionAmount === 'number'
        ? item.reductionAmount
        : parseFloat(item.reductionAmount) || 0,
    provisionAmount:
      typeof item.provisionAmount === 'number'
        ? item.provisionAmount
        : parseFloat(item.provisionAmount) || 0,
    selected: Boolean(selectedRows[idx]),
  }));

  // 计算累计金额
  const calculateTotalAmounts = () => {
    // 筛选出选中的行
    const selectedItems = processedResults.filter(item => selectedRows[item.id]);

    // 计算选中行的新增金额和坏账准备金额的总和
    // 注意：这里我们假设搜索结果中的坏账准备对应provisionAmount字段
    const totalNewAmount = selectedItems.reduce((sum, item) => sum + (item.newAmount || 0), 0);
    const totalProvisionAmount = selectedItems.reduce(
      (sum, item) => sum + (item.provisionAmount || 0),
      0,
    );

    // 当前表单中的金额
    const currentNewAmount = parseFloat(formData.overdueAmount) || 0;
    const currentProvisionAmount = parseFloat(formData.provisionAmount) || 0;

    return {
      totalNewAmount: totalNewAmount + currentNewAmount,
      totalProvisionAmount: totalProvisionAmount + currentProvisionAmount,
    };
  };

  // 计算累计金额
  const totals = calculateTotalAmounts();

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="md">
        <Box sx={styles.root}>
          <Paper sx={styles.paper} elevation={0}>
            <Box
              sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}
            >
              <Typography variant="h5" sx={{ ...styles.title, mb: 0 }}>
                新增逾期债权
              </Typography>
              <DownloadExcelButton
                type="NewDebtDetails"
                buttonText={`${currentYear}年新增债权下载`}
              />
            </Box>
            <Divider sx={{ mb: 2 }} />

            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                {/* 基本信息部分 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={styles.sectionTitle}>
                    基本信息
                  </Typography>
                  <Grid container spacing={1.5}>
                    <Grid item xs={12} md={6}>
                      <FormInput
                        label="债权人"
                        value={formData.creditor}
                        onChange={e => {
                          const newValue = e.target.value;
                          setFormData({ ...formData, creditor: newValue });
                          if (newValue && formData.debtor) {
                            // 当债权人和债务人都有值时，自动搜索
                            autoSearch(newValue, formData.debtor);
                          }
                        }}
                        required={true}
                        error={errors.creditor}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormInput
                        label="债务人"
                        value={formData.debtor}
                        onChange={e => {
                          const newValue = e.target.value;
                          setFormData({ ...formData, debtor: newValue });
                          if (formData.creditor && newValue) {
                            // 当债权人和债务人都有值时，自动搜索
                            autoSearch(formData.creditor, newValue);
                          }
                        }}
                        required={true}
                        error={errors.debtor}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                {/* 自动检索结果显示区域 */}
                {hasSearched && searchResults.length > 0 && (
                  <Grid item xs={12}>
                    <Box sx={styles.searchResults}>
                      <Typography variant="subtitle1" sx={styles.resultsTitle}>
                        已有债权债务记录 ({searchResults.length} 条记录)
                      </Typography>
                      {isSearching ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : (
                        <GenericDataTable
                          columns={[
                            { field: 'creditor', headerName: '债权人', width: '16%' },
                            { field: 'debtor', headerName: '债务人', width: '16%' },
                            { field: 'period', headerName: '归属期间', width: '10%' },
                            { field: 'isLitigation', headerName: '是否诉讼', width: '8%' },
                            { field: 'managementCompany', headerName: '管理公司', width: '10%' },
                            {
                              field: 'newAmount',
                              headerName: '新增金额',
                              width: '10%',
                              type: 'number',
                            },
                            {
                              field: 'reductionAmount',
                              headerName: '处置金额',
                              width: '10%',
                              type: 'number',
                            },
                            {
                              field: 'debtBalance',
                              headerName: '剩余债权',
                              width: '10%',
                              type: 'number',
                            },
                          ]}
                          data={processedResults}
                          rowHeight={40}
                          compact={true}
                          formatCurrency={formatCurrency}
                          renderActions={row => (
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%',
                              }}
                              onClick={() => handleCheckboxClick(row.id)}
                            >
                              <Box
                                sx={{
                                  width: '18px',
                                  height: '18px',
                                  border: '1px solid #aaa',
                                  borderRadius: '2px',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  bgcolor: row.selected ? '#1976d2' : 'transparent',
                                  color: 'white',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s',
                                  '&:hover': {
                                    bgcolor: row.selected ? '#1565c0' : '#f5f5f5',
                                  },
                                }}
                              >
                                {row.selected && '✓'}
                              </Box>
                            </Box>
                          )}
                          actionColumnWidth="6%"
                          actionColumnTitle="更新"
                        />
                      )}
                    </Box>
                  </Grid>
                )}
                {/* 管理信息部分 */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={styles.sectionTitle}>
                    管理信息
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormSelect
                        label="管理公司"
                        value={formData.managementCompany}
                        onChange={e =>
                          setFormData({ ...formData, managementCompany: e.target.value })
                        }
                        options={
                          isAdmin
                            ? managementCompanies.map(company => ({
                              value: company,
                              label: company,
                            }))
                            : [
                              {
                                value: user?.company || user?.companyname || '',
                                label:
                                    user?.company || user?.companyname || '请联系管理员设置公司',
                              },
                            ]
                        }
                        disabled={fieldsReadOnly.managementCompany || !isAdmin}
                        SelectProps={{
                          MenuProps: {
                            PaperProps: {
                              sx: styles.managementCompanySelect,
                            },
                            anchorOrigin: {
                              vertical: 'bottom',
                              horizontal: 'left',
                            },
                            transformOrigin: {
                              vertical: 'top',
                              horizontal: 'left',
                            },
                          },
                          sx: {
                            width: '100%',
                            '& .MuiSelect-select': {
                              padding: '8px 16px',
                              fontSize: '14px',
                            },
                            '& .MuiOutlinedInput-notchedOutline': {
                              borderColor: '#e0e0e0',
                            },
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              borderColor: '#bdbdbd',
                            },
                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                              borderColor: '#1976d2',
                            },
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormInput
                        label="科目名称"
                        value={formData.subjectName}
                        onChange={e => setFormData({ ...formData, subjectName: e.target.value })}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormSelect
                        label="是否涉诉"
                        value={formData.isLitigation || '否'}
                        onChange={e => setFormData({ ...formData, isLitigation: e.target.value })}
                        options={[
                          { value: '是', label: '是' },
                          { value: '否', label: '否' },
                        ]}
                        disabled={fieldsReadOnly.isLitigation} // 现在使用布尔值正确控制只读状态
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormInput
                        label="责任人"
                        value={formData.responsiblePerson}
                        onChange={e =>
                          setFormData({ ...formData, responsiblePerson: e.target.value })
                        }
                      />
                    </Grid>
                  </Grid>
                </Grid>
                {/* 金额信息部分 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={styles.sectionTitle}>
                    金额信息
                  </Typography>
                  <Grid container spacing={1.5}>
                    <Grid item xs={12} md={3}>
                      <FormInput
                        label="新增逾期金额"
                        value={formData.overdueAmount}
                        onChange={e => {
                          setFormData({ ...formData, overdueAmount: e.target.value });
                        }}
                        required={true}
                        error={errors.overdueAmount}
                        type="number"
                        isNumberFormat={true}
                        decimalPlaces={2}
                        warnings={inputWarnings}
                        onWarning={(label, warning) => {
                          setInputWarnings(prev => ({
                            ...prev,
                            [label]: warning,
                          }));
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography
                        variant="body2"
                        component="label"
                        sx={{
                          display: 'block',
                          mb: 0.5,
                          fontSize: '13px',
                          fontWeight: 500,
                          color: '#333',
                        }}
                      >
                        累计新增金额
                      </Typography>
                      <Box
                        sx={{
                          height: '32px', // 与 FormInput 保持一致
                          backgroundColor: '#f8f9fa',
                          border: '1px solid #e0e0e0',
                          borderRadius: '4px',
                          padding: '0 10px',
                          display: 'flex',
                          alignItems: 'center',
                          color: '#333',
                          fontSize: '13px',
                          pointerEvents: 'none', // 禁止所有鼠标交互
                          boxSizing: 'border-box',
                        }}
                      >
                        {formatCurrency(totals.totalNewAmount).replace('¥', '')}
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormInput
                        label="坏账准备计提金额"
                        value={formData.provisionAmount}
                        onChange={e =>
                          setFormData({ ...formData, provisionAmount: e.target.value })
                        }
                        required={true}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography
                        variant="body2"
                        component="label"
                        sx={{
                          display: 'block',
                          mb: 0.5,
                          fontSize: '13px',
                          fontWeight: 500,
                          color: '#333',
                        }}
                      >
                        累计坏账准备计提金额
                      </Typography>
                      <Box
                        sx={{
                          height: '32px', // 与 FormInput 保持一致
                          backgroundColor: '#f8f9fa',
                          border: '1px solid #e0e0e0',
                          borderRadius: '4px',
                          padding: '0 10px',
                          display: 'flex',
                          alignItems: 'center',
                          color: '#333',
                          fontSize: '13px',
                          pointerEvents: 'none', // 禁止所有鼠标交互
                          boxSizing: 'border-box',
                        }}
                      >
                        {formatCurrency(totals.totalProvisionAmount).replace('¥', '')}
                      </Box>
                    </Grid>
                  </Grid>
                </Grid>

                {/* 债权信息部分 */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={styles.sectionTitle}>
                    债权信息
                  </Typography>
                  <Grid container spacing={1.5}>
                    <Grid item xs={12} md={3}>
                      <FormSelect
                        label="债权类型"
                        value={formData.debtCategory}
                        onChange={e => setFormData({ ...formData, debtCategory: e.target.value })}
                        options={debtCategoryOptions}
                        required={true}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormSelect
                        label="债权性质"
                        value={formData.debtNature}
                        onChange={e => setFormData({ ...formData, debtNature: e.target.value })}
                        options={debtNatureOptions}
                        required={true}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormMonthPicker
                        label="逾期所属期间"
                        value={formData.addDate}
                        onChange={e => {
                          const selectedMonth = e.target.value; // 格式：YYYY-MM

                          // 计算选定月份的最后一天
                          const [year, month] = selectedMonth
                            .split('-')
                            .map(num => parseInt(num, 10));
                          // 获取下个月的第一天，然后减去一天，得到当前月份的最后一天
                          const lastDay = new Date(year, month, 0).getDate();
                          // 格式化为YYYY-MM-DD格式
                          const lastDayOfMonth = `${selectedMonth}-${String(lastDay).padStart(
                            2,
                            '0',
                          )}`;

                          // 更新表单数据，同时更新逾期日期
                          setFormData({
                            ...formData,
                            addDate: selectedMonth,
                            overdueDate: lastDayOfMonth,
                          });
                        }}
                        required={true}
                        name="addDate"
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormDatePicker
                        label="逾期日期"
                        value={formData.overdueDate}
                        onChange={e => setFormData({ ...formData, overdueDate: e.target.value })}
                        required={false}
                        name="overdueDate"
                        mode="date"
                      />
                    </Grid>
                  </Grid>
                </Grid>
                {/* 诉讼信息部分 */}
                {formData.isLitigation === '是' && (
                  <Grid item xs={12}>
                    <Typography variant="h6" sx={styles.sectionTitle}>
                      诉讼信息
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="案件名称"
                          value={formData.caseName}
                          onChange={e => setFormData({ ...formData, caseName: e.target.value })}
                          required={true}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="本金"
                          value={formData.litigationClaim.principal}
                          onChange={e =>
                            setFormData({
                              ...formData,
                              litigationClaim: {
                                ...formData.litigationClaim,
                                principal: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="利息"
                          value={formData.litigationClaim.interest}
                          onChange={e =>
                            setFormData({
                              ...formData,
                              litigationClaim: {
                                ...formData.litigationClaim,
                                interest: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="罚息"
                          value={formData.litigationClaim.penalty}
                          onChange={e =>
                            setFormData({
                              ...formData,
                              litigationClaim: {
                                ...formData.litigationClaim,
                                penalty: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="诉讼费"
                          value={formData.litigationClaim.litigationFee}
                          onChange={e =>
                            setFormData({
                              ...formData,
                              litigationClaim: {
                                ...formData.litigationClaim,
                                litigationFee: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormInput
                          label="中介费"
                          value={formData.litigationClaim.intermediaryFee}
                          onChange={e =>
                            setFormData({
                              ...formData,
                              litigationClaim: {
                                ...formData.litigationClaim,
                                intermediaryFee: e.target.value,
                              },
                            })
                          }
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                )}

                {/* 按钮部分 */}
                <Grid item xs={12}>
                  <Box sx={{ mt: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Button
                          variant="contained"
                          fullWidth
                          type="submit"
                          sx={styles.submitButton}
                        >
                          提交
                        </Button>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Button
                          variant="contained"
                          fullWidth
                          onClick={resetForm}
                          sx={styles.resetButton}
                        >
                          重置
                        </Button>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>

                {/* 当年新增债权记录列表 */}
                <Grid item xs={12}>
                  <Paper
                    sx={{ mt: 4, p: 2, borderRadius: 1, boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)' }}
                  >
                    <Typography
                      variant="subtitle1"
                      sx={{ fontSize: '15px', fontWeight: 500, color: '#283593', mb: 2 }}
                    >
                      当年新增债权记录
                    </Typography>
                    {isLoadingAddRecords && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
                        <CircularProgress size={24} color="info" />
                      </Box>
                    )}
                    <GenericDataTable
                      data={addRecords.length > 0 ? addRecords : []}
                      columns={[
                        {
                          field: 'id',
                          headerName: '序号',
                          width: '5%',
                          renderCell: (params, index) => {
                            return index + 1;
                          },
                        },
                        { field: 'creditor', headerName: '债权人', width: '20%' },
                        { field: 'debtor', headerName: '债务人', width: '20%' },
                        { field: 'managementCompany', headerName: '管理公司', width: '15%' },
                        { field: 'isLitigation', headerName: '是否涉诉', width: '10%' },
                        { field: 'month', headerName: '月份', width: '10%' },
                        {
                          field: 'debtAmount',
                          headerName: '新增金额',
                          width: '10%',
                          renderCell: params => {
                            // 判断值是否为零或null
                            const value = params.value || 0;
                            // 显示带两位小数的金额
                            return parseFloat(value).toFixed(2) + ' 万元';
                          },
                        },
                      ]}
                      pagination
                      rowHeight={40}
                      renderActions={params => {
                        return (
                          <>
                            <Button
                              variant="text"
                              color="error"
                              size="small"
                              onClick={() => handleDeleteRecord(params)}
                              disabled={deletingRecordIds[params.id] || false}
                              sx={{
                                minWidth: 'auto',
                                padding: '4px 8px',
                                fontSize: '12px',
                              }}
                            >
                              {deletingRecordIds[params.id] ? '处理中...' : '删除'}
                            </Button>
                          </>
                        );
                      }}
                      actionColumnWidth="15%"
                      actionColumnTitle="操作"
                      compact={true}
                      emptyMessage="暂无新增债权数据记录"
                    />
                  </Paper>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default OverdueDebtAdd;
