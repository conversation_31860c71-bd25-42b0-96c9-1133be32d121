import api from '../../../utils/api';

export const fetchDebtStatistics = async (year, month, company) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
    } else {
    }

    const response = await api.get('/debts/statistics', {
      params: {
        year,
        month,
        company,
      },
    });

    return response.data;
  } catch (error) {
    console.error('获取债权统计数据失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

export const fetchDebtStatisticsDetail = async (year, month, company) => {
  try {
    // 检查认证令牌是否存在
    const token = localStorage.getItem('token');
    if (!token) {
    }

    const response = await api.get('/debts/statistics/detail', {
      params: {
        year,
        month,
        company,
      },
    });

    console.log(
      '债权统计详情获取成功:',
      '新增债权条目数:',
      (response.data.newDebtDetailList || []).length,
      '减少债权条目数:',
      (response.data.reductionDebtDetailList || []).length,
    );
    return response.data;
  } catch (error) {
    console.error('获取债权统计详情失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};
