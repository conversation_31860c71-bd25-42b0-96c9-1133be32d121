/**
 * 命令行界面控制器
 * 实现类似Claude Code的命令选择界面
 */

class CommandInterface {
  constructor() {
    this.isActive = false;
    this.commandHistory = [];
    this.availableCommands = {
      'auto-confirm': {
        description: '启用全自动确认模式',
        action: () => this.enableAutoMode(),
        options: [
          { key: '1', text: '是，启用自动确认', value: true },
          { key: '2', text: '否，保持手动确认', value: false },
        ],
      },
      'auto-mode': {
        description: '配置自动化模式',
        action: () => this.configureAutoMode(),
        options: [
          { key: '1', text: '开发模式 (快速自动确认)', value: 'development' },
          { key: '2', text: '安全模式 (谨慎自动确认)', value: 'safe' },
          { key: '3', text: '演示模式 (延迟自动确认)', value: 'demo' },
          { key: '4', text: '禁用自动模式', value: 'disabled' },
        ],
      },
      toggle: {
        description: '切换自动确认状态',
        action: () => this.toggleAutoConfirm(),
        options: [],
      },
      status: {
        description: '查看当前状态',
        action: () => this.showStatus(),
        options: [],
      },
      help: {
        description: '显示帮助信息',
        action: () => this.showHelp(),
        options: [],
      },
    };

    this.initializeInterface();
  }

  /**
   * 初始化界面
   */
  initializeInterface() {
    // 创建全局快捷键
    document.addEventListener('keydown', e => {
      // Ctrl+Shift+/ 或 Cmd+Shift+/ 触发命令界面
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === '?') {
        e.preventDefault();
        this.showCommandInterface();
      }

      // ESC 关闭命令界面
      if (e.key === 'Escape' && this.isActive) {
        this.hideCommandInterface();
      }
    });

    // 添加全局命令函数
    window.autoCommand = this.showCommandInterface.bind(this);
    window.toggleAuto = this.toggleAutoConfirm.bind(this);
  }

  /**
   * 显示命令界面
   */
  showCommandInterface() {
    if (this.isActive) {
      return;
    }

    this.isActive = true;
    this.createCommandModal();
  }

  /**
   * 创建命令模态框
   */
  createCommandModal() {
    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.id = 'command-interface-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    `;

    // 创建命令窗口
    const commandWindow = document.createElement('div');
    commandWindow.style.cssText = `
      background: #1a1a1a;
      color: #fff;
      border-radius: 12px;
      padding: 24px;
      min-width: 600px;
      max-width: 800px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
      border: 1px solid #333;
    `;

    // 标题
    const title = document.createElement('div');
    title.style.cssText = `
      font-size: 18px;
      font-weight: bold;
      color: #4CAF50;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    `;
    title.innerHTML = `
      <span>🤖</span>
      <span>自动化控制中心</span>
      <span style="font-size: 12px; color: #888; margin-left: auto;">ESC 关闭</span>
    `;

    // 当前状态显示
    const statusDisplay = document.createElement('div');
    statusDisplay.style.cssText = `
      background: #2a2a2a;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 20px;
      font-size: 14px;
      border-left: 4px solid #4CAF50;
    `;
    this.updateStatusDisplay(statusDisplay);

    // 命令列表
    const commandList = document.createElement('div');
    commandList.style.cssText = `
      margin-bottom: 20px;
    `;

    const commandTitle = document.createElement('div');
    commandTitle.style.cssText = `
      font-size: 14px;
      color: #888;
      margin-bottom: 12px;
    `;
    commandTitle.textContent = '可用命令:';

    commandList.appendChild(commandTitle);

    // 添加命令选项
    Object.entries(this.availableCommands).forEach(([command, config], index) => {
      const commandItem = document.createElement('div');
      commandItem.style.cssText = `
        padding: 12px;
        margin-bottom: 8px;
        background: #2a2a2a;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid transparent;
        display: flex;
        align-items: center;
        gap: 12px;
      `;

      commandItem.innerHTML = `
        <span style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; min-width: 24px; text-align: center;">/${command}</span>
        <span style="flex: 1;">${config.description}</span>
      `;

      // 悬停效果
      commandItem.addEventListener('mouseenter', () => {
        commandItem.style.background = '#333';
        commandItem.style.borderColor = '#4CAF50';
      });

      commandItem.addEventListener('mouseleave', () => {
        commandItem.style.background = '#2a2a2a';
        commandItem.style.borderColor = 'transparent';
      });

      // 点击执行命令
      commandItem.addEventListener('click', () => {
        this.executeCommand(command, overlay);
      });

      commandList.appendChild(commandItem);
    });

    // 组装界面
    commandWindow.appendChild(title);
    commandWindow.appendChild(statusDisplay);
    commandWindow.appendChild(commandList);

    overlay.appendChild(commandWindow);
    document.body.appendChild(overlay);

    // 点击遮罩关闭
    overlay.addEventListener('click', e => {
      if (e.target === overlay) {
        this.hideCommandInterface();
      }
    });
  }

  /**
   * 更新状态显示
   */
  updateStatusDisplay(statusElement) {
    const controller = window.autoConfirmController;
    if (!controller) {
      statusElement.innerHTML = '❌ 自动确认控制器未加载';
      return;
    }

    const status = controller.getStatus();
    const statusColor = status.enabled ? '#4CAF50' : '#f44336';
    const statusText = status.enabled ? '启用' : '禁用';
    const behaviorText = status.defaultBehavior ? '自动确认' : '自动拒绝';

    statusElement.innerHTML = `
      <div style="display: flex; gap: 16px; align-items: center;">
        <span style="color: ${statusColor};">● ${statusText}</span>
        <span>行为: ${behaviorText}</span>
        <span>延迟: ${status.delay}ms</span>
        <span>通知: ${status.showNotifications ? '开启' : '关闭'}</span>
      </div>
    `;
  }

  /**
   * 执行命令
   */
  executeCommand(command, overlay) {
    const config = this.availableCommands[command];
    if (!config) {
      return;
    }

    // 如果有选项，显示选项界面
    if (config.options && config.options.length > 0) {
      this.showCommandOptions(command, config, overlay);
    } else {
      // 直接执行命令
      config.action();
      this.hideCommandInterface();
    }
  }

  /**
   * 显示命令选项
   */
  showCommandOptions(command, config, overlay) {
    // 清空当前内容
    const commandWindow = overlay.querySelector('div');
    commandWindow.innerHTML = '';

    // 标题
    const title = document.createElement('div');
    title.style.cssText = `
      font-size: 18px;
      font-weight: bold;
      color: #4CAF50;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    `;
    title.innerHTML = `
      <span>⚙️</span>
      <span>/${command}</span>
      <span style="font-size: 12px; color: #888; margin-left: auto;">ESC 返回</span>
    `;

    // 描述
    const description = document.createElement('div');
    description.style.cssText = `
      color: #ccc;
      margin-bottom: 20px;
      font-size: 14px;
    `;
    description.textContent = config.description;

    // 选项列表
    const optionsList = document.createElement('div');
    config.options.forEach((option, index) => {
      const optionItem = document.createElement('div');
      optionItem.style.cssText = `
        padding: 12px;
        margin-bottom: 8px;
        background: #2a2a2a;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid transparent;
        display: flex;
        align-items: center;
        gap: 12px;
      `;

      optionItem.innerHTML = `
        <span style="background: #2196F3; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; min-width: 24px; text-align: center;">${option.key}</span>
        <span style="flex: 1;">${option.text}</span>
      `;

      // 悬停效果
      optionItem.addEventListener('mouseenter', () => {
        optionItem.style.background = '#333';
        optionItem.style.borderColor = '#2196F3';
      });

      optionItem.addEventListener('mouseleave', () => {
        optionItem.style.background = '#2a2a2a';
        optionItem.style.borderColor = 'transparent';
      });

      // 点击选择
      optionItem.addEventListener('click', () => {
        this.executeCommandWithOption(command, option.value);
        this.hideCommandInterface();
      });

      optionsList.appendChild(optionItem);
    });

    // 返回按钮
    const backButton = document.createElement('div');
    backButton.style.cssText = `
      padding: 12px;
      margin-top: 16px;
      background: #333;
      border-radius: 6px;
      cursor: pointer;
      text-align: center;
      color: #ccc;
      transition: all 0.2s;
    `;
    backButton.textContent = '← 返回命令列表';
    backButton.addEventListener('click', () => {
      this.hideCommandInterface();
      setTimeout(() => this.showCommandInterface(), 100);
    });

    commandWindow.appendChild(title);
    commandWindow.appendChild(description);
    commandWindow.appendChild(optionsList);
    commandWindow.appendChild(backButton);

    // 键盘快捷键
    const handleKeyPress = e => {
      const option = config.options.find(opt => opt.key === e.key);
      if (option) {
        this.executeCommandWithOption(command, option.value);
        this.hideCommandInterface();
        document.removeEventListener('keydown', handleKeyPress);
      }
    };
    document.addEventListener('keydown', handleKeyPress);
  }

  /**
   * 执行带选项的命令
   */
  executeCommandWithOption(command, optionValue) {
    const controller = window.autoConfirmController;
    if (!controller) {
      console.warn('自动确认控制器未找到');
      return;
    }

    switch (command) {
    case 'auto-confirm':
      if (optionValue) {
        controller.enable();
      } else {
        controller.disable();
      }
      break;

    case 'auto-mode':
      switch (optionValue) {
      case 'development':
        controller.enable();
        controller.setBehavior(true);
        controller.setDelay(200);
        controller.setNotifications(true);
        break;
      case 'safe':
        controller.enable();
        controller.setBehavior(false);
        controller.setDelay(1000);
        controller.setNotifications(true);
        break;
      case 'demo':
        controller.enable();
        controller.setBehavior(true);
        controller.setDelay(1500);
        controller.setNotifications(true);
        break;
      case 'disabled':
        controller.disable();
        break;
      }
      break;
    }

    // 记录命令历史
    this.commandHistory.push({
      command,
      option: optionValue,
      timestamp: new Date(),
    });
  }

  /**
   * 隐藏命令界面
   */
  hideCommandInterface() {
    const overlay = document.getElementById('command-interface-overlay');
    if (overlay) {
      overlay.style.opacity = '0';
      setTimeout(() => {
        if (document.body.contains(overlay)) {
          document.body.removeChild(overlay);
        }
      }, 200);
    }
    this.isActive = false;
  }

  /**
   * 切换自动确认
   */
  toggleAutoConfirm() {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.toggle();
      return controller.isEnabled;
    }
    return false;
  }

  /**
   * 启用自动模式
   */
  enableAutoMode() {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.enable();
      controller.setBehavior(true);
      controller.setDelay(500);
    }
  }

  /**
   * 配置自动模式（通过选项界面处理）
   */
  configureAutoMode() {
    // 这个方法通过选项界面处理
  }

  /**
   * 显示状态
   */
  showStatus() {
    const controller = window.autoConfirmController;
    if (controller) {
      controller.status();
    }
  }

  /**
   * 显示帮助
   */
  showHelp() {
    // 显示提示消息
    const controller = window.autoConfirmController;
    if (controller) {
      controller.showMessage('帮助信息已在控制台显示', 'info', 3000);
    }
  }
}

// 创建全局实例
const commandInterface = new CommandInterface();

export default commandInterface;
