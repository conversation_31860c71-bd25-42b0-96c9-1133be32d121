import React, { useState } from 'react';
import { Box, Paper, Typography, Button, Grid } from '@mui/material';
import { FormInput, FormSelect, FormDatePicker } from '../components/forms';

const FormComponentsDemo = () => {
  const [formData, setFormData] = useState({
    customerName: '',
    amount: '',
    email: '',
    debtType: '',
    companies: [],
    startDate: '',
    endDateTime: '',
  });

  const [errors, setErrors] = useState({});

  const handleChange = e => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSubmit = e => {
    e.preventDefault();
    const newErrors = {};

    // Simple validation
    if (!formData.customerName) {
      newErrors.customerName = '请输入客户名称';
    }
    if (!formData.amount) {
      newErrors.amount = '请输入金额';
    }
    if (!formData.debtType) {
      newErrors.debtType = '请选择债权类型';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    alert('表单提交成功！\n' + JSON.stringify(formData, null, 2));
  };

  const debtTypeOptions = [
    { value: 'loan', label: '贷款' },
    { value: 'credit', label: '信用债' },
    { value: 'bond', label: '债券' },
    { value: 'other', label: '其他' },
  ];

  const companyOptions = [
    { value: 'company1', label: '上海金融公司' },
    { value: 'company2', label: '北京投资集团' },
    { value: 'company3', label: '深圳资产管理' },
    { value: 'company4', label: '广州金融服务' },
    { value: 'company5', label: '杭州投资有限' },
  ];

  return (
    <Box sx={{ maxWidth: 800, margin: '0 auto', padding: 3 }}>
      <Typography variant="h4" gutterBottom align="center" sx={{ marginBottom: 4 }}>
        标准化表单组件演示
      </Typography>

      <Paper sx={{ padding: 4, marginBottom: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ marginBottom: 3 }}>
          表单组件示例
        </Typography>

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* 基础文本输入 */}
            <Grid item xs={12} md={6}>
              <FormInput
                label="客户名称"
                name="customerName"
                value={formData.customerName}
                onChange={handleChange}
                required
                placeholder="请输入客户名称"
                error={errors.customerName}
              />
            </Grid>

            {/* 数字输入（带格式化） */}
            <Grid item xs={12} md={6}>
              <FormInput
                label="债权金额"
                name="amount"
                type="number"
                value={formData.amount}
                onChange={handleChange}
                required
                placeholder="请输入债权金额"
                isNumberFormat
                decimalPlaces={2}
                minValue={0}
                error={errors.amount}
              />
            </Grid>

            {/* 邮箱输入 */}
            <Grid item xs={12} md={6}>
              <FormInput
                label="邮箱地址"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="请输入邮箱地址"
                error={errors.email}
              />
            </Grid>

            {/* 单选下拉框 */}
            <Grid item xs={12} md={6}>
              <FormSelect
                label="债权类型"
                name="debtType"
                value={formData.debtType}
                onChange={handleChange}
                required
                options={debtTypeOptions}
                error={errors.debtType}
              />
            </Grid>

            {/* 多选下拉框（带搜索） */}
            <Grid item xs={12}>
              <FormSelect
                label="关联公司"
                name="companies"
                value={formData.companies}
                onChange={handleChange}
                multiple
                searchable
                searchPlaceholder="搜索公司..."
                options={companyOptions}
                placeholder="请选择关联公司"
              />
            </Grid>

            {/* 日期选择器 */}
            <Grid item xs={12} md={6}>
              <FormDatePicker
                label="开始日期"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                mode="date"
                placeholder="请选择开始日期"
              />
            </Grid>

            {/* 日期时间选择器 */}
            <Grid item xs={12} md={6}>
              <FormDatePicker
                label="结束时间"
                name="endDateTime"
                value={formData.endDateTime}
                onChange={handleChange}
                mode="datetime"
                placeholder="请选择结束时间"
              />
            </Grid>

            {/* 提交按钮 */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', marginTop: 2 }}>
                <Button type="submit" variant="contained" color="primary" size="medium">
                  提交表单
                </Button>
                <Button
                  type="button"
                  variant="outlined"
                  color="secondary"
                  size="medium"
                  onClick={() => {
                    setFormData({
                      customerName: '',
                      amount: '',
                      email: '',
                      debtType: '',
                      companies: [],
                      startDate: '',
                      endDateTime: '',
                    });
                    setErrors({});
                  }}
                >
                  重置表单
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      {/* 当前表单数据预览 */}
      <Paper sx={{ padding: 3 }}>
        <Typography variant="h6" gutterBottom>
          当前表单数据
        </Typography>
        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
      </Paper>
    </Box>
  );
};

export default FormComponentsDemo;
