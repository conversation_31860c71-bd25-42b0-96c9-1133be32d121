/**
 * ErrorMessage Component
 *
 * Displays error messages with proper styling and different error types
 * Supports validation, network, and general error types
 * Has dismissible and persistent modes
 * Uses consistent error colors and typography from unified style constants
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Alert, AlertTitle, Box, IconButton, Typography, Collapse } from '@mui/material';
import {
  ErrorOutline as ErrorIcon,
  WarningAmber as WarningIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  NetworkWifi as NetworkIcon,
  BugReport as ValidationIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Import unified style constants
import { colors, spacing, shadows, typography, transitions } from 'constants/styleConstants';

// Styled components
const StyledAlert = styled(Alert)(({ theme, errorType }) => ({
  marginBottom: spacing.sm,
  borderRadius: '6px',
  boxShadow: shadows.sm,
  transition: transitions.all,

  // Custom styling based on error type
  ...(errorType === 'validation' && {
    backgroundColor: '#fff3e0',
    borderLeft: `4px solid ${colors.warning.main}`,
    color: colors.warning.dark,
  }),

  ...(errorType === 'network' && {
    backgroundColor: '#f3e5f5',
    borderLeft: `4px solid ${colors.error.main}`,
    color: colors.error.dark,
  }),

  ...(errorType === 'general' && {
    backgroundColor: '#ffebee',
    borderLeft: `4px solid ${colors.error.main}`,
    color: colors.error.dark,
  }),
}));

const ErrorHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: spacing.xs,
  marginBottom: spacing.xs,
}));

const ErrorContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: spacing.xs,
}));

const ErrorTitle = styled(Typography)(({ theme }) => ({
  fontSize: typography.fontSize.md,
  fontWeight: typography.fontWeight.semibold,
  color: 'inherit',
}));

const ErrorDescription = styled(Typography)(({ theme }) => ({
  fontSize: typography.fontSize.sm,
  color: colors.text.secondary,
  lineHeight: typography.lineHeight.normal,
}));

const ErrorActions = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: spacing.xs,
  marginTop: spacing.sm,
}));

// Error type configurations
const ERROR_CONFIGS = {
  validation: {
    icon: ValidationIcon,
    severity: 'warning',
    defaultTitle: '验证错误',
    defaultMessage: '请检查输入的数据格式是否正确',
  },
  network: {
    icon: NetworkIcon,
    severity: 'error',
    defaultTitle: '网络错误',
    defaultMessage: '网络连接失败，请检查网络连接后重试',
  },
  general: {
    icon: ErrorIcon,
    severity: 'error',
    defaultTitle: '系统错误',
    defaultMessage: '操作失败，请稍后重试',
  },
};

const ErrorMessage = ({
  type = 'general',
  title = '',
  message = '',
  details = '',
  dismissible = true,
  persistent = false,
  onDismiss = null,
  actions = null,
  className = '',
  ...props
}) => {
  const [visible, setVisible] = useState(true);
  const [collapsed, setCollapsed] = useState(false);

  const config = ERROR_CONFIGS[type] || ERROR_CONFIGS.general;
  const IconComponent = config.icon;

  const errorTitle = title || config.defaultTitle;
  const errorMessage = message || config.defaultMessage;

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
    setVisible(false);
  };

  const toggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  if (!visible && !persistent) {
    return null;
  }

  return (
    <StyledAlert
      severity={config.severity}
      errorType={type}
      className={className}
      action={
        dismissible && (
          <IconButton
            size="small"
            onClick={handleDismiss}
            sx={{
              color: 'inherit',
              opacity: 0.7,
              '&:hover': { opacity: 1 },
            }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        )
      }
      {...props}
    >
      <ErrorContent>
        <ErrorHeader>
          <IconComponent fontSize="small" />
          <ErrorTitle variant="subtitle2">{errorTitle}</ErrorTitle>
        </ErrorHeader>

        <ErrorDescription>{errorMessage}</ErrorDescription>

        {details && (
          <Box>
            <Typography
              variant="caption"
              sx={{
                cursor: 'pointer',
                color: colors.primary.main,
                textDecoration: 'underline',
                '&:hover': { color: colors.primary.dark },
              }}
              onClick={toggleCollapse}
            >
              {collapsed ? '隐藏详细信息' : '显示详细信息'}
            </Typography>

            <Collapse in={collapsed}>
              <Box
                sx={{
                  mt: spacing.xs,
                  p: spacing.sm,
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  borderRadius: '4px',
                  fontFamily: typography.fontFamily.mono,
                  fontSize: typography.fontSize.xs,
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                }}
              >
                {details}
              </Box>
            </Collapse>
          </Box>
        )}

        {actions && <ErrorActions>{actions}</ErrorActions>}
      </ErrorContent>
    </StyledAlert>
  );
};

ErrorMessage.propTypes = {
  /**
   * Type of error (validation, network, general)
   */
  type: PropTypes.oneOf(['validation', 'network', 'general']),

  /**
   * Error title (optional, uses default based on type)
   */
  title: PropTypes.string,

  /**
   * Error message (optional, uses default based on type)
   */
  message: PropTypes.string,

  /**
   * Detailed error information (optional)
   */
  details: PropTypes.string,

  /**
   * Whether the error can be dismissed
   */
  dismissible: PropTypes.bool,

  /**
   * Whether the error persists after dismissal
   */
  persistent: PropTypes.bool,

  /**
   * Callback when error is dismissed
   */
  onDismiss: PropTypes.func,

  /**
   * Custom action buttons/elements
   */
  actions: PropTypes.node,

  /**
   * Additional CSS class name
   */
  className: PropTypes.string,
};

ErrorMessage.defaultProps = {
  type: 'general',
  title: '',
  message: '',
  details: '',
  dismissible: true,
  persistent: false,
  onDismiss: null,
  actions: null,
  className: '',
};

export default ErrorMessage;
