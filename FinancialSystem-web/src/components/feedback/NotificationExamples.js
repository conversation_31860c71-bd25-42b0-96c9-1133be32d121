/**
 * NotificationExamples - 通知系统使用示例
 * 
 * 展示如何使用统一的通知管理系统
 */

import React from 'react';
import { Button, Box, Typography, Grid, Paper } from '@mui/material';
import { useNotifications } from './NotificationManager';

const NotificationExamples = () => {
  const notifications = useNotifications();

  // 基础通知示例
  const showSuccessNotification = () => {
    notifications.success('操作成功完成！', {
      title: '成功',
      duration: 3000,
    });
  };

  const showErrorNotification = () => {
    notifications.error('操作失败，请重试', {
      title: '错误',
      duration: 6000,
    });
  };

  const showWarningNotification = () => {
    notifications.warning('请注意数据完整性', {
      title: '警告',
      duration: 5000,
    });
  };

  const showInfoNotification = () => {
    notifications.info('系统将在5分钟后维护', {
      title: '系统通知',
      duration: 4000,
    });
  };

  // 对话框示例
  const showConfirmDialog = () => {
    notifications.confirm('确定要删除这条记录吗？', {
      title: '确认删除',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: () => {
        notifications.success('记录已删除');
      },
      onCancel: () => {
        notifications.info('操作已取消');
      },
    });
  };

  const showSaveDialog = () => {
    notifications.showDialog('save', {
      title: '保存更改',
      message: '确定要保存当前的更改吗？',
      confirmText: '保存',
      cancelText: '取消',
      onConfirm: () => {
        notifications.success('更改已保存');
      },
    });
  };

  // Toast示例
  const showToast = () => {
    notifications.toast('快速提示消息', 'info');
  };

  const showSuccessToast = () => {
    notifications.toast('操作成功', 'success');
  };

  // 自定义配置示例
  const showCustomNotification = () => {
    notifications.showNotification('info', '这是一个自定义配置的通知', {
      title: '自定义通知',
      duration: 8000,
      // 可以添加更多自定义配置
    });
  };

  // 业务场景示例
  const simulateFormSubmit = () => {
    // 显示加载状态
    const loadingId = notifications.showNotification('info', '正在提交数据...', {
      title: '处理中',
      duration: 0, // 不自动消失
    });

    // 模拟异步操作
    setTimeout(() => {
      notifications.hideNotification(loadingId);
      
      // 随机成功或失败
      if (Math.random() > 0.5) {
        notifications.success('表单提交成功！', {
          title: '提交成功',
        });
      } else {
        notifications.error('提交失败，请检查网络连接', {
          title: '提交失败',
        });
      }
    }, 2000);
  };

  const simulateDataValidation = () => {
    notifications.warning('请填写必填字段：姓名、邮箱', {
      title: '表单验证',
      duration: 7000, // 验证错误需要更长时间
    });
  };

  const simulateNetworkError = () => {
    notifications.error('网络连接超时，请检查网络设置后重试', {
      title: '网络错误',
      duration: 8000, // 网络错误需要更长时间
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        通知系统示例
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
        所有弹窗都会在页面正中央显示，并根据类型自动消失
      </Typography>

      <Grid container spacing={3}>
        {/* 基础通知 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              基础通知
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button variant="contained" color="success" onClick={showSuccessNotification}>
                成功通知 (3秒)
              </Button>
              <Button variant="contained" color="error" onClick={showErrorNotification}>
                错误通知 (6秒)
              </Button>
              <Button variant="contained" color="warning" onClick={showWarningNotification}>
                警告通知 (5秒)
              </Button>
              <Button variant="contained" color="info" onClick={showInfoNotification}>
                信息通知 (4秒)
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* 对话框 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              对话框
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button variant="outlined" onClick={showConfirmDialog}>
                确认对话框
              </Button>
              <Button variant="outlined" onClick={showSaveDialog}>
                保存对话框
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Toast提示 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Toast提示
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button variant="text" onClick={showToast}>
                普通Toast (2秒)
              </Button>
              <Button variant="text" color="success" onClick={showSuccessToast}>
                成功Toast (2秒)
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* 业务场景 */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              业务场景
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button variant="contained" onClick={simulateFormSubmit}>
                模拟表单提交
              </Button>
              <Button variant="contained" color="warning" onClick={simulateDataValidation}>
                表单验证错误 (7秒)
              </Button>
              <Button variant="contained" color="error" onClick={simulateNetworkError}>
                网络错误 (8秒)
              </Button>
              <Button variant="outlined" onClick={showCustomNotification}>
                自定义配置 (8秒)
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* 使用说明 */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          使用说明
        </Typography>
        <Typography variant="body2" component="div">
          <strong>自动消失时长（行业标准）：</strong>
          <ul>
            <li>成功消息：3秒 - 用户需要确认但不需要长时间停留</li>
            <li>信息消息：4秒 - 给用户足够时间阅读</li>
            <li>警告消息：5秒 - 需要用户注意</li>
            <li>错误消息：6秒 - 重要信息需要充分注意</li>
            <li>表单验证：7秒 - 需要用户修正错误</li>
            <li>网络错误：8秒 - 重要错误信息</li>
            <li>Toast提示：2秒 - 快速反馈</li>
            <li>确认对话框：不自动消失，需要用户操作</li>
          </ul>
        </Typography>
      </Paper>
    </Box>
  );
};

export default NotificationExamples;
