/**
 * LoadingSpinner Component
 *
 * A flexible loading spinner component with Material-UI CircularProgress
 * Supports different sizes, overlay and inline modes
 * Uses the unified style constants for consistent styling
 */

import PropTypes from 'prop-types';
import { CircularProgress, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

// Import unified style constants
import { colors, spacing, shadows, zIndex, transitions } from 'constants/styleConstants';

// Styled components
const LoadingOverlay = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'column',
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  zIndex: zIndex.modal,
  transition: transitions.opacity,
}));

const LoadingContainer = styled(Box)(({ theme, overlay }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'column',
  gap: spacing.sm,
  ...(overlay && {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: zIndex.dropdown,
  }),
}));

const LoadingText = styled(Typography)(({ theme }) => ({
  color: colors.text.secondary,
  fontSize: '14px',
  fontWeight: 400,
  marginTop: spacing.xs,
}));

// Size mapping
const SIZE_MAP = {
  small: 20,
  medium: 40,
  large: 60,
};

const LoadingSpinner = ({
  size = 'medium',
  overlay = false,
  fullScreen = false,
  message = '',
  color = 'primary',
  className = '',
  ...props
}) => {
  const spinnerSize = SIZE_MAP[size] || SIZE_MAP.medium;

  // Full screen overlay mode
  if (fullScreen) {
    return (
      <LoadingOverlay className={className} {...props}>
        <CircularProgress size={spinnerSize} color={color} thickness={4} />
        {message && <LoadingText variant="body2">{message}</LoadingText>}
      </LoadingOverlay>
    );
  }

  // Overlay mode (relative to parent)
  if (overlay) {
    return (
      <LoadingContainer overlay className={className} {...props}>
        <CircularProgress size={spinnerSize} color={color} thickness={4} />
        {message && <LoadingText variant="body2">{message}</LoadingText>}
      </LoadingContainer>
    );
  }

  // Inline mode
  return (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="center"
      flexDirection="column"
      gap={spacing.xs}
      className={className}
      {...props}
    >
      <CircularProgress size={spinnerSize} color={color} thickness={4} />
      {message && <LoadingText variant="body2">{message}</LoadingText>}
    </Box>
  );
};

LoadingSpinner.propTypes = {
  /**
   * Size of the spinner
   */
  size: PropTypes.oneOf(['small', 'medium', 'large']),

  /**
   * Whether to show as overlay (relative to parent container)
   */
  overlay: PropTypes.bool,

  /**
   * Whether to show as full screen overlay
   */
  fullScreen: PropTypes.bool,

  /**
   * Optional loading message to display
   */
  message: PropTypes.string,

  /**
   * Color of the spinner
   */
  color: PropTypes.oneOf([
    'primary',
    'secondary',
    'error',
    'info',
    'success',
    'warning',
    'inherit',
  ]),

  /**
   * Additional CSS class name
   */
  className: PropTypes.string,
};

LoadingSpinner.defaultProps = {
  size: 'medium',
  overlay: false,
  fullScreen: false,
  message: '',
  color: 'primary',
  className: '',
};

export default LoadingSpinner;
