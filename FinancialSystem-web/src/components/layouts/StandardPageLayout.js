import React from 'react';
import PropTypes from 'prop-types';
import { Box, Container, Paper, Typography, Divider, Breadcrumbs, Link } from '@mui/material';
import { useLocation } from 'react-router-dom';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import { colors, typography, spacing, dimensions, shadows } from 'constants/styleConstants';

/**
 * StandardPageLayout - 标准页面布局组件
 *
 * 提供统一的页面结构，包括：
 * - 页头（标题、面包屑）
 * - 内容区域
 * - 响应式设计
 * - 一致的样式
 */
const StandardPageLayout = ({
  title,
  subtitle,
  children,
  actions,
  breadcrumbs,
  maxWidth = 'lg',
  showPaper = true,
  paperElevation = 0,
  spacing: customSpacing = 'normal',
  showDivider = true,
  ...props
}) => {
  const location = useLocation();

  // 根据spacing类型设置内容间距
  const getSpacingValue = () => {
    switch (customSpacing) {
    case 'compact':
      return spacing.md;
    case 'comfortable':
      return spacing['3xl'];
    case 'normal':
    default:
      return spacing['2xl'];
    }
  };

  // 生成默认面包屑
  const generateDefaultBreadcrumbs = () => {
    const pathnames = location.pathname.split('/').filter(x => x);
    return pathnames.map((name, index) => {
      const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
      const isLast = index === pathnames.length - 1;

      return {
        name: name.charAt(0).toUpperCase() + name.slice(1),
        href: isLast ? null : routeTo,
        isLast,
      };
    });
  };

  // 使用传入的面包屑或生成默认面包屑
  const finalBreadcrumbs = breadcrumbs || generateDefaultBreadcrumbs();

  // 标准页面样式
  const pageStyles = {
    container: {
      width: '100%',
      minHeight: '100vh',
    },
    paper: {
      padding: getSpacingValue(),
      margin: `${spacing.sm} 0`,
      borderRadius: dimensions.borderRadius.base,
      boxShadow: shadows.card,
      backgroundColor: colors.background.paper,
    },
    header: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: getSpacingValue(),
    },
    headerContent: {
      flex: 1,
    },
    title: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.semibold,
      color: colors.text.primary,
      marginBottom: subtitle ? spacing.xs : 0,
    },
    subtitle: {
      fontSize: typography.fontSize.sm,
      color: colors.text.secondary,
      marginBottom: 0,
    },
    actions: {
      display: 'flex',
      gap: spacing.sm,
      alignItems: 'center',
    },
    breadcrumbs: {
      marginBottom: spacing.lg,
      fontSize: typography.fontSize.sm,
    },
    divider: {
      marginBottom: getSpacingValue(),
    },
    content: {
      width: '100%',
    },
  };

  const renderContent = () => (
    <Box sx={pageStyles.container}>
      {/* 面包屑导航 */}
      {finalBreadcrumbs.length > 0 && (
        <Breadcrumbs sx={pageStyles.breadcrumbs}>
          <Link color="inherit" href="/">
            首页
          </Link>
          {finalBreadcrumbs.map((crumb, index) =>
            crumb.isLast ? (
              <Typography key={index} color="text.primary">
                {crumb.name}
              </Typography>
            ) : (
              <Link key={index} color="inherit" href={crumb.href}>
                {crumb.name}
              </Link>
            ),
          )}
        </Breadcrumbs>
      )}

      {/* 页面内容 */}
      {showPaper ? (
        <Paper sx={pageStyles.paper} elevation={paperElevation}>
          {/* 页头 */}
          <Box sx={pageStyles.header}>
            <Box sx={pageStyles.headerContent}>
              {title && (
                <Typography variant="h1" sx={pageStyles.title}>
                  {title}
                </Typography>
              )}
              {subtitle && (
                <Typography variant="body2" sx={pageStyles.subtitle}>
                  {subtitle}
                </Typography>
              )}
            </Box>
            {actions && <Box sx={pageStyles.actions}>{actions}</Box>}
          </Box>

          {/* 分隔线 */}
          {showDivider && (title || subtitle || actions) && <Divider sx={pageStyles.divider} />}

          {/* 主要内容 */}
          <Box sx={pageStyles.content}>{children}</Box>
        </Paper>
      ) : (
        <Box sx={pageStyles.content}>
          {/* 页头 */}
          <Box sx={pageStyles.header}>
            <Box sx={pageStyles.headerContent}>
              {title && (
                <Typography variant="h1" sx={pageStyles.title}>
                  {title}
                </Typography>
              )}
              {subtitle && (
                <Typography variant="body2" sx={pageStyles.subtitle}>
                  {subtitle}
                </Typography>
              )}
            </Box>
            {actions && <Box sx={pageStyles.actions}>{actions}</Box>}
          </Box>

          {/* 分隔线 */}
          {showDivider && (title || subtitle || actions) && <Divider sx={pageStyles.divider} />}

          {/* 主要内容 */}
          {children}
        </Box>
      )}
    </Box>
  );

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth={maxWidth} {...props}>
        {renderContent()}
      </Container>
    </DashboardLayout>
  );
};

StandardPageLayout.propTypes = {
  // 页面标题
  title: PropTypes.string,
  // 副标题
  subtitle: PropTypes.string,
  // 页面内容
  children: PropTypes.node.isRequired,
  // 页面操作按钮
  actions: PropTypes.node,
  // 面包屑导航
  breadcrumbs: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      href: PropTypes.string,
      isLast: PropTypes.bool,
    }),
  ),
  // 容器最大宽度
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]),
  // 是否显示纸张背景
  showPaper: PropTypes.bool,
  // 纸张阴影深度
  paperElevation: PropTypes.number,
  // 间距类型
  spacing: PropTypes.oneOf(['compact', 'normal', 'comfortable']),
  // 是否显示分隔线
  showDivider: PropTypes.bool,
};

export default StandardPageLayout;
