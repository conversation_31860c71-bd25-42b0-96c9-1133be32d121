import React from 'react';
import PropTypes from 'prop-types';
import { Box, Grid, Paper, Typography, Divider, Button, Alert } from '@mui/material';
import StandardPageLayout from './StandardPageLayout';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
} from 'constants/styleConstants';

/**
 * FormPageLayout - 表单页面布局组件
 *
 * 专为表单页面优化的布局，提供：
 * - 搜索/筛选区域
 * - 表单主体区域
 * - 结果/反馈区域
 * - 多步骤表单支持
 * - 表单验证反馈
 */
const FormPageLayout = ({
  title,
  subtitle,
  searchSection,
  children,
  resultsSection,
  actions,
  footerActions,
  formValidation,
  showSteps = false,
  currentStep = 0,
  steps = [],
  maxWidth = 'md',
  spacing: customSpacing = 'normal',
  sections = [],
  ...props
}) => {
  // 表单专用样式
  const formStyles = {
    searchSection: {
      marginBottom: spacing['2xl'],
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      boxShadow: shadows.base,
      backgroundColor: colors.background.light,
    },
    searchTitle: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.secondary.main,
      marginBottom: spacing.md,
    },
    formSection: {
      marginTop: spacing.lg,
    },
    sectionTitle: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.secondary.main,
      marginBottom: spacing.md,
    },
    sectionDivider: {
      marginBottom: spacing.lg,
    },
    resultsSection: {
      marginTop: spacing['2xl'],
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      boxShadow: shadows.base,
      backgroundColor: colors.background.light,
    },
    resultsTitle: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.primary.main,
      marginBottom: spacing.sm,
    },
    stepIndicator: {
      marginBottom: spacing['2xl'],
      padding: spacing.lg,
      borderRadius: dimensions.borderRadius.base,
      backgroundColor: colors.background.light,
    },
    stepItem: {
      display: 'flex',
      alignItems: 'center',
      padding: spacing.sm,
      borderRadius: dimensions.borderRadius.sm,
      cursor: 'pointer',
      transition: transitions.all,
    },
    stepItemActive: {
      backgroundColor: colors.primary.main,
      color: colors.primary.contrastText,
    },
    stepItemCompleted: {
      backgroundColor: colors.success.light,
      color: colors.success.contrastText,
    },
    stepItemPending: {
      backgroundColor: colors.grey[200],
      color: colors.text.secondary,
    },
    validationAlert: {
      marginBottom: spacing.lg,
    },
    footerActions: {
      marginTop: spacing['2xl'],
      padding: spacing.lg,
      backgroundColor: colors.background.light,
      borderRadius: dimensions.borderRadius.base,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    buttonGroup: {
      display: 'flex',
      gap: spacing.sm,
    },
  };

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    if (!showSteps || steps.length === 0) {
      return null;
    }

    return (
      <Box sx={formStyles.stepIndicator}>
        <Typography variant="h6" sx={{ marginBottom: spacing.md }}>
          步骤进度
        </Typography>
        <Grid container spacing={1}>
          {steps.map((step, index) => {
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            const isPending = index > currentStep;

            let stepStyle = formStyles.stepItem;
            if (isActive) {
              stepStyle = { ...stepStyle, ...formStyles.stepItemActive };
            } else if (isCompleted) {
              stepStyle = { ...stepStyle, ...formStyles.stepItemCompleted };
            } else {
              stepStyle = { ...stepStyle, ...formStyles.stepItemPending };
            }

            return (
              <Grid item xs={12 / steps.length} key={index}>
                <Box sx={stepStyle}>
                  <Typography variant="body2" sx={{ fontWeight: typography.fontWeight.medium }}>
                    {index + 1}. {step.title}
                  </Typography>
                </Box>
              </Grid>
            );
          })}
        </Grid>
      </Box>
    );
  };

  // 渲染表单验证反馈
  const renderValidationFeedback = () => {
    if (!formValidation) {
      return null;
    }

    const { hasErrors, errors, warnings, success } = formValidation;

    return (
      <Box sx={formStyles.validationAlert}>
        {hasErrors && errors.length > 0 && (
          <Alert severity="error" sx={{ marginBottom: spacing.sm }}>
            <Typography variant="body2" sx={{ fontWeight: typography.fontWeight.medium }}>
              请修正以下错误：
            </Typography>
            <ul style={{ margin: 0, paddingLeft: spacing.lg }}>
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </Alert>
        )}

        {warnings && warnings.length > 0 && (
          <Alert severity="warning" sx={{ marginBottom: spacing.sm }}>
            <Typography variant="body2" sx={{ fontWeight: typography.fontWeight.medium }}>
              注意事项：
            </Typography>
            <ul style={{ margin: 0, paddingLeft: spacing.lg }}>
              {warnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </Alert>
        )}

        {success && <Alert severity="success">{success}</Alert>}
      </Box>
    );
  };

  // 渲染表单区块
  const renderFormSections = () => {
    if (sections.length === 0) {
      return children;
    }

    return sections.map((section, index) => (
      <Box key={index} sx={formStyles.formSection}>
        {section.title && (
          <>
            <Typography variant="subtitle1" sx={formStyles.sectionTitle}>
              {section.title}
            </Typography>
            <Divider sx={formStyles.sectionDivider} />
          </>
        )}
        {section.content}
      </Box>
    ));
  };

  return (
    <StandardPageLayout
      title={title}
      subtitle={subtitle}
      actions={actions}
      maxWidth={maxWidth}
      spacing={customSpacing}
      {...props}
    >
      {/* 步骤指示器 */}
      {renderStepIndicator()}

      {/* 表单验证反馈 */}
      {renderValidationFeedback()}

      {/* 搜索/筛选区域 */}
      {searchSection && (
        <Box sx={formStyles.searchSection}>
          <Typography variant="subtitle1" sx={formStyles.searchTitle}>
            搜索筛选
          </Typography>
          {searchSection}
        </Box>
      )}

      {/* 表单主体区域 */}
      <Box component="form" noValidate>
        {renderFormSections()}
      </Box>

      {/* 结果显示区域 */}
      {resultsSection && (
        <Box sx={formStyles.resultsSection}>
          <Typography variant="subtitle1" sx={formStyles.resultsTitle}>
            结果
          </Typography>
          {resultsSection}
        </Box>
      )}

      {/* 底部操作区域 */}
      {footerActions && (
        <Box sx={formStyles.footerActions}>
          <Box sx={formStyles.buttonGroup}>{footerActions}</Box>
        </Box>
      )}
    </StandardPageLayout>
  );
};

FormPageLayout.propTypes = {
  // 页面标题
  title: PropTypes.string,
  // 副标题
  subtitle: PropTypes.string,
  // 搜索筛选区域
  searchSection: PropTypes.node,
  // 表单主体内容
  children: PropTypes.node,
  // 结果显示区域
  resultsSection: PropTypes.node,
  // 顶部操作按钮
  actions: PropTypes.node,
  // 底部操作按钮
  footerActions: PropTypes.node,
  // 表单验证状态
  formValidation: PropTypes.shape({
    hasErrors: PropTypes.bool,
    errors: PropTypes.arrayOf(PropTypes.string),
    warnings: PropTypes.arrayOf(PropTypes.string),
    success: PropTypes.string,
  }),
  // 是否显示步骤指示器
  showSteps: PropTypes.bool,
  // 当前步骤索引
  currentStep: PropTypes.number,
  // 步骤配置
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      description: PropTypes.string,
    }),
  ),
  // 容器最大宽度
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]),
  // 间距类型
  spacing: PropTypes.oneOf(['compact', 'normal', 'comfortable']),
  // 表单区块配置
  sections: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      content: PropTypes.node.isRequired,
    }),
  ),
};

export default FormPageLayout;
