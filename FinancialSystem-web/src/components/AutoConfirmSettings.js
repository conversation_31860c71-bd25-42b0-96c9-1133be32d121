import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Switch,
  FormControlLabel,
  Typography,
  Box,
  Divider,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Chip,
  Grid,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  AutoMode as AutoModeIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import {
  getAutoConfirmConfig,
  updateAutoConfirmConfig,
  resetAutoConfirmConfig,
} from '../utils/autoConfirmService';

const AutoConfirmSettings = ({ open, onClose }) => {
  const [config, setConfig] = useState(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (open) {
      loadConfig();
    }
  }, [open]);

  const loadConfig = () => {
    const currentConfig = getAutoConfirmConfig();
    setConfig(currentConfig);
    setHasChanges(false);
  };

  const handleGlobalToggle = enabled => {
    const newConfig = { ...config, enabled };
    setConfig(newConfig);
    setHasChanges(true);
  };

  const handleOperationToggle = (operation, field, value) => {
    const newConfig = {
      ...config,
      operations: {
        ...config.operations,
        [operation]: {
          ...config.operations[operation],
          [field]: value,
        },
      },
    };
    setConfig(newConfig);
    setHasChanges(true);
  };

  const handleDelayChange = (operation, delay) => {
    handleOperationToggle(operation, 'delay', delay);
  };

  const handleSave = () => {
    updateAutoConfirmConfig(config);
    setHasChanges(false);
    onClose();
  };

  const handleReset = () => {
    resetAutoConfirmConfig();
    loadConfig();
  };

  const operationLabels = {
    delete: '删除操作',
    revokePermission: '权限撤销',
    batchOperation: '批量操作',
    dataSubmission: '数据提交',
    expirePermissions: '过期权限',
  };

  const operationDescriptions = {
    delete: '删除债权记录、处置记录等删除操作',
    revokePermission: '撤销用户权限、公司权限等操作',
    batchOperation: '批量处理、批量更新等操作',
    dataSubmission: '表单提交、数据保存等操作',
    expirePermissions: '批量过期权限等不可逆操作',
  };

  if (!config) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth scroll="body">
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <SettingsIcon />
          <Typography variant="h6">自动确认设置</Typography>
          {config.enabled && (
            <Chip label="已启用" color="primary" size="small" icon={<AutoModeIcon />} />
          )}
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* 全局设置 */}
        <Box mb={3}>
          <FormControlLabel
            control={
              <Switch
                checked={config.enabled}
                onChange={e => handleGlobalToggle(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  启用自动确认功能
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  开启后，可以自动处理各种确认对话框，提高操作效率
                </Typography>
              </Box>
            }
          />
        </Box>

        {config.enabled && (
          <Alert severity="info" icon={<WarningIcon />} sx={{ mb: 3 }}>
            自动确认功能已启用。请谨慎配置各项操作，避免误操作导致的数据丢失。
          </Alert>
        )}

        <Divider sx={{ my: 2 }} />

        {/* 默认行为设置 */}
        <Box mb={3}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            默认行为
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={config.defaultBehavior}
                onChange={e =>
                  setConfig({
                    ...config,
                    defaultBehavior: e.target.checked,
                  }) & setHasChanges(true)
                }
                color="primary"
                disabled={!config.enabled}
              />
            }
            label={
              <Typography variant="body2">
                未配置的操作默认{config.defaultBehavior ? '自动确认' : '自动取消'}
              </Typography>
            }
          />
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* 操作类型配置 */}
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          操作类型配置
        </Typography>

        {Object.entries(config.operations).map(([operation, operationConfig]) => (
          <Accordion key={operation} sx={{ mb: 1 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" width="100%">
                <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>
                  {operationLabels[operation] || operation}
                </Typography>
                <Box display="flex" gap={1} mr={2}>
                  {operationConfig.enabled && (
                    <Chip
                      label={operationConfig.autoConfirm ? '自动确认' : '自动取消'}
                      color={operationConfig.autoConfirm ? 'success' : 'warning'}
                      size="small"
                    />
                  )}
                  {operationConfig.delay > 0 && (
                    <Chip label={`${operationConfig.delay}ms`} variant="outlined" size="small" />
                  )}
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {operationDescriptions[operation] || '自定义操作类型'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={operationConfig.enabled}
                        onChange={e =>
                          handleOperationToggle(operation, 'enabled', e.target.checked)
                        }
                        color="primary"
                        disabled={!config.enabled}
                      />
                    }
                    label="启用自动处理"
                  />
                </Grid>

                {operationConfig.enabled && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={operationConfig.autoConfirm}
                            onChange={e =>
                              handleOperationToggle(operation, 'autoConfirm', e.target.checked)
                            }
                            color="primary"
                          />
                        }
                        label={operationConfig.autoConfirm ? '自动确认' : '自动取消'}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="body2" gutterBottom>
                        延迟时间: {operationConfig.delay}ms
                      </Typography>
                      <Slider
                        value={operationConfig.delay}
                        onChange={(e, value) => handleDelayChange(operation, value)}
                        min={0}
                        max={5000}
                        step={100}
                        marks={[
                          { value: 0, label: '立即' },
                          { value: 1000, label: '1秒' },
                          { value: 3000, label: '3秒' },
                          { value: 5000, label: '5秒' },
                        ]}
                        valueLabelDisplay="auto"
                        sx={{ mt: 1 }}
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>
        ))}

        <Divider sx={{ my: 3 }} />

        {/* 开发设置 */}
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          开发设置
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={config.development.logEnabled}
                  onChange={e =>
                    setConfig({
                      ...config,
                      development: {
                        ...config.development,
                        logEnabled: e.target.checked,
                      },
                    }) & setHasChanges(true)
                  }
                  color="primary"
                  disabled={!config.enabled}
                />
              }
              label="启用控制台日志"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={config.development.showToast}
                  onChange={e =>
                    setConfig({
                      ...config,
                      development: {
                        ...config.development,
                        showToast: e.target.checked,
                      },
                    }) & setHasChanges(true)
                  }
                  color="primary"
                  disabled={!config.enabled}
                />
              }
              label="显示操作提示"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleReset} color="warning">
          重置配置
        </Button>
        <Button onClick={onClose}>取消</Button>
        <Button onClick={handleSave} variant="contained" disabled={!hasChanges}>
          保存设置
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AutoConfirmSettings;
