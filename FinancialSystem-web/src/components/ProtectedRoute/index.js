import React, { useState, useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import PropTypes from 'prop-types';
import MDBox from 'components/MDBox';
import CircularProgress from '@mui/material/CircularProgress';

const ProtectedRoute = ({ adminOnly }) => {
  const { isAuthenticated, user, loading } = useAuth();
  const location = useLocation();
  const [localChecked, setLocalChecked] = useState(false);

  // 简单日志记录
  useEffect(() => {}, [location.pathname, isAuthenticated, loading]);

  // 检查本地存储
  useEffect(() => {
    if (!isAuthenticated && !loading && !localChecked) {
      // 简单检查本地存储是否有token，但不尝试验证它
      setLocalChecked(true);
    }
  }, [isAuthenticated, loading, localChecked]);

  // 加载中
  if (loading) {
    return (
      <MDBox display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </MDBox>
    );
  }

  // 未认证，重定向到登录
  if (!isAuthenticated) {
    return <Navigate to="/authentication/sign-in" state={{ from: location.pathname }} replace />;
  }

  // 检查管理员权限
  if (adminOnly && user?.role !== 'ROLE_ADMIN') {
    return <Navigate to="/" replace />;
  }

  // 认证通过，渲染子路由
  return <Outlet />;
};

ProtectedRoute.propTypes = {
  adminOnly: PropTypes.bool,
};

ProtectedRoute.defaultProps = {
  adminOnly: false,
};

export default ProtectedRoute;
