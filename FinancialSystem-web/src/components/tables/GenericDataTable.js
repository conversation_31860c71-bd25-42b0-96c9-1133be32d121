import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * GenericDataTable 组件 - 通用数据表格组件
 * 使用 HTML table 展示数据，支持固定百分比列宽、紧凑样式以及数值格式化。
 * 同时增加简单分页功能，每页显示 pageSize 行数据，超过部分分页显示。
 *
 * @param {Object} props
 * @param {Array} props.columns - 列定义数组，每项对象包含以下属性：
 *   - field: 数据字段名（用于从每行数据中获取值）
 *   - headerName: 列标题
 *   - width: 列宽（百分比字符串，例如 '20%'）
 *   - align: 单元格对齐方式，可选（默认：数字列为 'right'，其他为 'center'）
 *   - type: 数据类型，可选，例如 'number' 表示数值
 * @param {Array} props.data - 数据数组，每个元素为一行数据对象
 * @param {Function} [props.formatCurrency] - 数值格式化函数，传入数值，返回格式化后的字符串；默认格式为人民币
 * @param {number} [props.pageSize=10] - 每页显示的行数
 * @param {Function} [props.renderActions] - 自定义操作列渲染函数，接收行数据作为参数，返回React元素
 * @param {string} [props.actionColumnWidth='10%'] - 操作列的宽度，百分比字符串
 * @param {string} [props.actionColumnTitle='操作'] - 操作列的标题
 * @param {boolean} [props.compact=false] - 是否使用紧凑模式，紧凑模式下单元格内边距更小
 * @param {number} [props.rowHeight=40] - 行高设置，默认为40px
 * @param {number} [props.fontSize=13] - 表格字体大小，默认为13px
 */
const GenericDataTable = ({
  columns,
  data,
  formatCurrency,
  pageSize = 10,
  renderActions,
  actionColumnWidth = '10%',
  actionColumnTitle = '操作',
  compact = false,
  rowHeight = 40,
  fontSize = 13,
}) => {
  // 内置默认货币格式化函数
  const defaultFormatCurrency = value =>
    value !== null && value !== undefined
      ? new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2,
      }).format(value)
      : '-';

  const formatFn = formatCurrency || defaultFormatCurrency;

  // 分页状态：当前页码（从0开始）
  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = Math.ceil(data.length / pageSize);
  const pagedData = data.slice(currentPage * pageSize, (currentPage + 1) * pageSize);

  return (
    <div style={{ width: '100%', overflowX: 'auto', fontSize: `${fontSize}px` }}>
      <table
        style={{
          width: '100%',
          borderCollapse: 'collapse',
          tableLayout: 'fixed',
          backgroundColor: '#fff',
        }}
      >
        <thead style={{ backgroundColor: '#f5f5f5' }}>
          <tr>
            {columns.map((col, index) => (
              <th
                key={`header-${col.field || index}`}
                style={{
                  width: col.width || 'auto',
                  padding: compact ? '8px 2px' : '12px 4px',
                  textAlign: 'center',
                  borderBottom: '2px solid #e0e0e0',
                  fontWeight: 'bold',
                  height: `${rowHeight - 5}px !important`,
                  verticalAlign: 'middle',
                  overflow: 'hidden',
                }}
              >
                {col.headerName}
              </th>
            ))}
            {renderActions && (
              <th
                style={{
                  width: actionColumnWidth,
                  padding: compact ? '8px 2px' : '12px 4px',
                  textAlign: 'center',
                  borderBottom: '2px solid #e0e0e0',
                  fontWeight: 'bold',
                  height: `${rowHeight - 5}px !important`,
                  verticalAlign: 'middle',
                  overflow: 'hidden',
                }}
              >
                {actionColumnTitle}
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {pagedData.map((row, rowIdx) => (
            <tr
              key={`row-${rowIdx}-${row.id || rowIdx}`}
              style={{
                height: `${rowHeight}px`,
                ':hover': { backgroundColor: '#f9f9f9' },
              }}
            >
              {columns.map((col, colIdx) => {
                const value = row[col.field];
                // 如果没有设置对齐方式，则数字默认右对齐，其它默认居中
                const cellAlign = col.align || (col.type === 'number' ? 'right' : 'center');
                let displayValue = value;
                if (col.type === 'number') {
                  displayValue = formatFn(value);
                } else if (React.isValidElement(value)) {
                  // 如果值是 React 元素，直接使用
                  displayValue = value;
                }
                return (
                  <td
                    key={`cell-${rowIdx}-${col.field || colIdx}`}
                    style={{
                      padding: compact ? '6px 2px' : '10px 4px',
                      textAlign: cellAlign,
                      borderBottom: '1px solid #e0e0e0',
                      verticalAlign: 'middle',
                      height: `${rowHeight}px`,
                      overflow: 'hidden',
                    }}
                  >
                    {displayValue}
                  </td>
                );
              })}
              {renderActions && (
                <td
                  style={{
                    padding: compact ? '6px 2px' : '10px 4px',
                    textAlign: 'center',
                    borderBottom: '1px solid #e0e0e0',
                    verticalAlign: 'middle',
                    height: `${rowHeight}px`,
                    overflow: 'hidden',
                  }}
                >
                  {renderActions(row)}
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>

      {/* 分页控制 */}
      {totalPages > 1 && (
        <div
          style={{
            marginTop: '8px',
            textAlign: 'center',
            fontSize: `${Math.max(fontSize - 1, 12)}px`,
          }}
        >
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 0))}
            disabled={currentPage === 0}
            style={{
              padding: '4px 8px',
              marginRight: '8px',
              cursor: currentPage === 0 ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              border: '1px solid #d0d0d0',
              borderRadius: '3px',
              backgroundColor: '#f5f5f5',
            }}
          >
            上一页
          </button>
          <span style={{ margin: '0 8px', fontSize: `${Math.max(fontSize - 1, 12)}px` }}>
            第 {currentPage + 1} 页 / 共 {totalPages} 页
          </span>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages - 1))}
            disabled={currentPage >= totalPages - 1}
            style={{
              padding: '4px 8px',
              marginLeft: '8px',
              cursor: currentPage >= totalPages - 1 ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              border: '1px solid #d0d0d0',
              borderRadius: '3px',
              backgroundColor: '#f5f5f5',
            }}
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
};

GenericDataTable.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      field: PropTypes.string.isRequired,
      headerName: PropTypes.string.isRequired,
      width: PropTypes.string, // 百分比字符串，例如 '20%'
      align: PropTypes.string,
      type: PropTypes.string,
    }),
  ).isRequired,
  data: PropTypes.array.isRequired,
  formatCurrency: PropTypes.func,
  pageSize: PropTypes.number,
  renderActions: PropTypes.func,
  actionColumnWidth: PropTypes.string,
  actionColumnTitle: PropTypes.string,
  compact: PropTypes.bool,
  rowHeight: PropTypes.number,
  fontSize: PropTypes.number,
};

export default GenericDataTable;
