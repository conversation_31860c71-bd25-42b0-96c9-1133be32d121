import React, { useState } from 'react';
import {
  GenericDataTable,
  ImprovedGenericDataTable,
  EnhancedGenericDataTable,
  createTableColumn,
  createCurrencyColumn,
  createDateColumn,
  createBooleanColumn,
  createActionColumn,
  TABLE_CONFIGURATIONS,
  migrateTableProps,
} from './index';

/**
 * Table Usage Examples
 *
 * This file demonstrates how to use the different table components
 * and their various configurations.
 */

// Sample data for examples
const sampleData = [
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    age: 25,
    salary: 8000,
    department: '技术部',
    isActive: true,
    joinDate: '2023-01-15',
    performance: 85.5,
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    age: 30,
    salary: 12000,
    department: '销售部',
    isActive: true,
    joinDate: '2022-03-20',
    performance: 92.0,
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    age: 28,
    salary: 10000,
    department: '市场部',
    isActive: false,
    joinDate: '2021-08-10',
    performance: 78.5,
  },
  {
    id: 4,
    name: '赵六',
    email: '<PERSON><PERSON><PERSON><PERSON>@example.com',
    age: 35,
    salary: 15000,
    department: '技术部',
    isActive: true,
    joinDate: '2020-05-12',
    performance: 88.2,
  },
  {
    id: 5,
    name: '钱七',
    email: '<EMAIL>',
    age: 26,
    salary: 7500,
    department: '人事部',
    isActive: true,
    joinDate: '2023-06-01',
    performance: 82.8,
  },
];

// Example 1: Basic GenericDataTable (Original)
export const BasicTableExample = () => {
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const columns = [
    { field: 'name', headerName: '姓名', width: '15%' },
    { field: 'email', headerName: '邮箱', width: '25%' },
    { field: 'age', headerName: '年龄', width: '10%', type: 'number' },
    { field: 'salary', headerName: '薪资', width: '15%', type: 'number' },
    { field: 'department', headerName: '部门', width: '15%' },
    { field: 'isActive', headerName: '状态', width: '10%' },
    { field: 'joinDate', headerName: '入职日期', width: '15%' },
  ];

  const renderActions = row => (
    <div style={{ display: 'flex', gap: '8px' }}>
      <button onClick={() => showNotification(`查看 ${row.name}`)}>查看</button>
      <button onClick={() => showNotification(`编辑 ${row.name}`)}>编辑</button>
    </div>
  );

  return (
    <div>
      <h3>基础表格示例 (GenericDataTable)</h3>
      <GenericDataTable
        columns={columns}
        data={sampleData}
        pageSize={3}
        renderActions={renderActions}
        compact={false}
      />
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert severity={notification.severity}>{notification.message}</Alert>
      </Snackbar>
    </div>
  );
};

// Example 2: Improved GenericDataTable with Enhanced Styling
export const ImprovedTableExample = () => {
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const columns = [
    { field: 'name', headerName: '姓名', width: '15%' },
    { field: 'email', headerName: '邮箱', width: '25%' },
    { field: 'age', headerName: '年龄', width: '10%', type: 'number' },
    { field: 'salary', headerName: '薪资', width: '15%', type: 'number' },
    { field: 'department', headerName: '部门', width: '15%' },
    { field: 'isActive', headerName: '状态', width: '10%', type: 'boolean' },
    { field: 'joinDate', headerName: '入职日期', width: '15%', type: 'date' },
  ];

  const renderActions = row => (
    <div style={{ display: 'flex', gap: '8px' }}>
      <button onClick={() => showNotification(`查看 ${row.name}`)}>查看</button>
      <button onClick={() => showNotification(`编辑 ${row.name}`)}>编辑</button>
    </div>
  );

  return (
    <div>
      <h3>改进表格示例 (ImprovedGenericDataTable)</h3>
      <ImprovedGenericDataTable
        columns={columns}
        data={sampleData}
        pageSize={3}
        renderActions={renderActions}
        stripedRows={true}
        hoverRows={true}
        stickyHeader={true}
        caption="员工信息表"
        onRowClick={(event, row) => showNotification(`点击行: ${row.name}`)}
      />
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert severity={notification.severity}>{notification.message}</Alert>
      </Snackbar>
    </div>
  );
};

// Example 3: Enhanced GenericDataTable with Advanced Features
export const EnhancedTableExample = () => {
  const [selected, setSelected] = useState([]);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [searchValue, setSearchValue] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const columns = [
    createTableColumn({
      field: 'name',
      headerName: '姓名',
      width: 120,
      sortable: true,
      searchable: true,
    }),
    createTableColumn({
      field: 'email',
      headerName: '邮箱',
      width: 200,
      sortable: true,
      searchable: true,
    }),
    createTableColumn({
      field: 'age',
      headerName: '年龄',
      width: 80,
      type: 'number',
      sortable: true,
    }),
    createCurrencyColumn('salary', '薪资', 120),
    createTableColumn({
      field: 'department',
      headerName: '部门',
      width: 100,
      sortable: true,
      searchable: true,
    }),
    createBooleanColumn('isActive', '状态', 80),
    createDateColumn('joinDate', '入职日期', 120),
    createTableColumn({
      field: 'performance',
      headerName: '绩效',
      width: 80,
      type: 'number',
      render: value => `${value}%`,
    }),
  ];

  const actions = [
    {
      label: '查看',
      icon: '👁️',
      onClick: row => showNotification(`查看 ${row.name}`),
      tooltip: '查看详情',
    },
    {
      label: '编辑',
      icon: '✏️',
      onClick: row => showNotification(`编辑 ${row.name}`),
      tooltip: '编辑信息',
    },
    {
      label: '删除',
      icon: '🗑️',
      onClick: row => showNotification(`删除 ${row.name}`, 'warning'),
      tooltip: '删除员工',
      color: 'error',
      disabled: row => !row.isActive,
    },
  ];

  return (
    <div>
      <h3>增强表格示例 (EnhancedGenericDataTable)</h3>
      <EnhancedGenericDataTable
        columns={columns}
        data={sampleData}
        pageSize={5}
        pageSizeOptions={[5, 10, 20]}
        // Selection
        selectable={true}
        selected={selected}
        onSelectionChange={setSelected}
        // Sorting
        sortable={true}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={(field, order) => {
          setSortBy(field);
          setSortOrder(order);
        }}
        // Search
        searchable={true}
        searchValue={searchValue}
        onSearchChange={setSearchValue}
        // Actions
        actions={actions}
        // Styling
        stripedRows={true}
        hoverRows={true}
        stickyHeader={true}
        // Export
        exportable={true}
        onExport={() => showNotification('导出功能')}
        // Events
        onRowClick={(event, row) => showNotification(`点击行: ${row.name}`)}
        onRowDoubleClick={(event, row) => showNotification(`双击行: ${row.name}`)}
        onRefresh={() => showNotification('刷新数据')}
        caption="员工管理表格"
        ariaLabel="员工信息数据表"
      />

      <div style={{ marginTop: '16px' }}>
        <strong>已选择: {selected.length} 项</strong>
        {selected.length > 0 && <button onClick={() => setSelected([])}>清空选择</button>}
      </div>
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert severity={notification.severity}>{notification.message}</Alert>
      </Snackbar>
    </div>
  );
};

// Example 4: Using Table Configurations
export const ConfigurationExample = () => {
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const columns = [
    createTableColumn({ field: 'name', headerName: '姓名', width: 120 }),
    createTableColumn({ field: 'department', headerName: '部门', width: 100 }),
    createCurrencyColumn('salary', '薪资', 120),
    createBooleanColumn('isActive', '状态', 80),
  ];

  return (
    <div>
      <h3>配置示例</h3>

      <h4>紧凑模式</h4>
      <EnhancedGenericDataTable
        columns={columns}
        data={sampleData}
        {...TABLE_CONFIGURATIONS.COMPACT}
      />

      <h4>高级模式</h4>
      <EnhancedGenericDataTable
        columns={columns}
        data={sampleData}
        {...TABLE_CONFIGURATIONS.ADVANCED}
        onExport={() => showNotification('导出功能')}
      />

      <h4>只读模式</h4>
      <EnhancedGenericDataTable
        columns={columns}
        data={sampleData}
        {...TABLE_CONFIGURATIONS.READONLY}
      />
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert severity={notification.severity}>{notification.message}</Alert>
      </Snackbar>
    </div>
  );
};

// Example 5: Migration from Old to New
export const MigrationExample = () => {
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  const showNotification = (message, severity = 'info') => {
    setNotification({ open: true, message, severity });
  };

  // Old GenericDataTable configuration
  const oldTableProps = {
    columns: [
      { field: 'name', headerName: '姓名', width: '20%' },
      { field: 'salary', headerName: '薪资', width: '15%', type: 'number' },
      { field: 'department', headerName: '部门', width: '15%' },
    ],
    data: sampleData,
    pageSize: 5,
    renderActions: row => (
      <button onClick={() => showNotification(`操作 ${row.name}`)}>操作</button>
    ),
    actionColumnTitle: '操作',
    actionColumnWidth: '10%',
    compact: true,
    rowHeight: 35,
    fontSize: 12,
  };

  // Migrate to new format
  const newTableProps = migrateTableProps(oldTableProps);

  return (
    <div>
      <h3>迁移示例</h3>

      <h4>原始表格</h4>
      <GenericDataTable {...oldTableProps} />

      <h4>迁移后的表格</h4>
      <ImprovedGenericDataTable {...newTableProps} />
      <Snackbar
        open={notification.open}
        autoHideDuration={3000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert severity={notification.severity}>{notification.message}</Alert>
      </Snackbar>
    </div>
  );
};

// Example 6: Loading and Empty States
export const StateExample = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  const columns = [
    createTableColumn({ field: 'name', headerName: '姓名', width: 120 }),
    createTableColumn({ field: 'department', headerName: '部门', width: 100 }),
    createCurrencyColumn('salary', '薪资', 120),
  ];

  const simulateLoading = () => {
    setLoading(true);
    setData([]);
    setTimeout(() => {
      setLoading(false);
      setData(sampleData);
    }, 2000);
  };

  const clearData = () => {
    setData([]);
  };

  return (
    <div>
      <h3>状态示例</h3>

      <div style={{ marginBottom: '16px' }}>
        <button onClick={simulateLoading}>模拟加载</button>
        <button onClick={clearData} style={{ marginLeft: '8px' }}>
          清空数据
        </button>
        <button onClick={() => setData(sampleData)} style={{ marginLeft: '8px' }}>
          加载数据
        </button>
      </div>

      <EnhancedGenericDataTable
        columns={columns}
        data={data}
        loading={loading}
        loadingMessage="正在加载员工数据..."
        emptyMessage="暂无员工数据"
        pageSize={5}
      />
    </div>
  );
};

// Main component showcasing all examples
const TableExamples = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1>表格组件使用示例</h1>

      <div style={{ marginBottom: '40px' }}>
        <BasicTableExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <ImprovedTableExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <EnhancedTableExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <ConfigurationExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <MigrationExample />
      </div>

      <div style={{ marginBottom: '40px' }}>
        <StateExample />
      </div>
    </div>
  );
};

export default TableExamples;
