import PropTypes from 'prop-types';

import { Chart } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Chart.js configuration
import ChartJS from '../../../utils/chartConfig';

// Material Dashboard 2 React Base Styles
import colors from 'assets/theme/base/colors';
import typography from 'assets/theme/base/typography';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

const { gradients, dark } = colors;
const { fontFamily, fontWeightRegular, fontWeightMedium, fontWeightBold } = typography;

/**
 * StandardMixedChart - 标准化混合图表组件（柱状图+折线图）
 * @param {Object} props
 * @param {Array} props.labels - X轴标签数组
 * @param {Array} props.datasets - 数据集数组，每个数据集包含 { label, data, type, yAxisID, backgroundColor?, borderColor? }
 * @param {String} props.title - 图表标题
 * @param {Boolean} props.showDataLabels - 是否显示数据标签
 * @param {Boolean} props.showLegend - 是否显示图例
 * @param {Boolean} props.showGrid - 是否显示网格
 * @param {Object} props.leftAxisConfig - 左Y轴配置
 * @param {Object} props.rightAxisConfig - 右Y轴配置
 * @param {Object} props.xAxisConfig - X轴配置
 * @param {String} props.height - 图表高度
 * @param {String} props.width - 图表宽度
 * @param {Boolean} props.responsive - 是否响应式
 * @param {Object} props.customOptions - 自定义配置选项
 * @returns {JSX.Element}
 */
const StandardMixedChart = ({
  labels,
  datasets,
  title,
  showDataLabels = true,
  showLegend = true,
  showGrid = true,
  leftAxisConfig = {},
  rightAxisConfig = {},
  xAxisConfig = {},
  height = '400px',
  width = '100%',
  responsive = true,
  customOptions = {},
}) => {
  // 默认颜色配置
  const defaultColors = [
    gradients.info.main,
    gradients.success.main,
    gradients.warning.main,
    gradients.error.main,
    gradients.primary.main,
    gradients.secondary.main,
    gradients.dark.main,
  ];

  // 处理数据集，添加默认样式
  const processedDatasets = datasets.map((dataset, index) => {
    const baseColor = defaultColors[index % defaultColors.length];

    if (dataset.type === 'line') {
      return {
        ...dataset,
        type: 'line',
        borderColor: dataset.borderColor || baseColor,
        backgroundColor: dataset.backgroundColor || 'transparent',
        borderWidth: dataset.borderWidth || 3,
        tension: dataset.tension !== undefined ? dataset.tension : 0.4,
        fill: dataset.fill !== undefined ? dataset.fill : false,
        pointRadius: dataset.pointRadius !== undefined ? dataset.pointRadius : 4,
        pointHoverRadius: dataset.pointHoverRadius !== undefined ? dataset.pointHoverRadius : 6,
        pointBackgroundColor: dataset.pointBackgroundColor || baseColor,
        pointBorderColor: dataset.pointBorderColor || '#ffffff',
        pointBorderWidth: dataset.pointBorderWidth || 2,
        pointHoverBackgroundColor: dataset.pointHoverBackgroundColor || baseColor,
        pointHoverBorderColor: dataset.pointHoverBorderColor || '#ffffff',
        pointHoverBorderWidth: dataset.pointHoverBorderWidth || 2,
        yAxisID: dataset.yAxisID || 'y1',
      };
    } else {
      // 默认为柱状图
      return {
        ...dataset,
        type: 'bar',
        backgroundColor: dataset.backgroundColor || `${baseColor}CC`,
        borderColor: dataset.borderColor || baseColor,
        borderWidth: dataset.borderWidth || 1,
        borderRadius: dataset.borderRadius || 4,
        borderSkipped: dataset.borderSkipped || false,
        maxBarThickness: dataset.maxBarThickness || 60,
        categoryPercentage: dataset.categoryPercentage || 0.8,
        barPercentage: dataset.barPercentage || 0.9,
        yAxisID: dataset.yAxisID || 'y',
      };
    }
  });

  // 图表数据
  const chartData = {
    labels,
    datasets: processedDatasets,
  };

  // 默认配置选项
  const defaultOptions = {
    responsive,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    plugins: {
      legend: {
        display: showLegend,
        position: 'top',
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightRegular,
          },
          color: dark.main,
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          family: fontFamily,
          size: 16,
          weight: fontWeightBold,
        },
        color: dark.main,
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          family: fontFamily,
          size: 14,
          weight: fontWeightMedium,
        },
        bodyFont: {
          family: fontFamily,
          size: 12,
          weight: fontWeightRegular,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const suffix = context.dataset.yAxisID === 'y1' ? '' : '';
            return `${context.dataset.label}: ${context.parsed.y?.toLocaleString()}${suffix}`;
          },
        },
      },
      datalabels: {
        display: function (context) {
          // 根据数据类型决定是否显示标签
          if (!showDataLabels) {
            return false;
          }

          // 折线图不显示数据标签，避免过于拥挤
          if (context.dataset.type === 'line') {
            return false;
          }

          return true;
        },
        anchor: 'end',
        align: 'start',
        offset: 4,
        color: dark.main,
        font: {
          family: fontFamily,
          size: 10,
          weight: fontWeightMedium,
        },
        formatter: value => {
          return typeof value === 'number' ? value.toLocaleString() : value;
        },
        clip: true,
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1,
          drawBorder: true,
          drawTicks: true,
        },
        ticks: {
          color: dark.main,
          font: {
            family: fontFamily,
            size: 11,
            weight: fontWeightRegular,
          },
          maxRotation: labels && labels.length > 8 ? 45 : 0,
          minRotation: labels && labels.length > 8 ? 45 : 0,
          padding: 8,
        },
        title: {
          display: !!xAxisConfig.title,
          text: xAxisConfig.title,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightMedium,
          },
          color: dark.main,
        },
        ...xAxisConfig,
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1,
          drawBorder: true,
          drawTicks: true,
        },
        ticks: {
          color: dark.main,
          font: {
            family: fontFamily,
            size: 11,
            weight: fontWeightRegular,
          },
          padding: 8,
          callback: function (value) {
            return typeof value === 'number' ? value.toLocaleString() : value;
          },
        },
        title: {
          display: !!leftAxisConfig.title,
          text: leftAxisConfig.title,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightMedium,
          },
          color: dark.main,
        },
        beginAtZero: true,
        ...leftAxisConfig,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          color: dark.main,
          font: {
            family: fontFamily,
            size: 11,
            weight: fontWeightRegular,
          },
          padding: 8,
          callback: function (value) {
            return typeof value === 'number' ? value.toLocaleString() : value;
          },
        },
        title: {
          display: !!rightAxisConfig.title,
          text: rightAxisConfig.title,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightMedium,
          },
          color: dark.main,
        },
        beginAtZero: true,
        ...rightAxisConfig,
      },
    },
    elements: {
      line: {
        borderJoinStyle: 'round',
      },
      point: {
        hoverRadius: 8,
      },
    },
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20,
      },
    },
  };

  // 合并自定义选项
  const finalOptions = {
    ...defaultOptions,
    ...customOptions,
    plugins: {
      ...defaultOptions.plugins,
      ...customOptions.plugins,
    },
    scales: {
      ...defaultOptions.scales,
      ...customOptions.scales,
    },
  };

  return (
    <div
      style={{
        width,
        height,
        position: 'relative',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        padding: '16px',
        overflow: 'hidden',
      }}
    >
      <Chart type="bar" data={chartData} options={finalOptions} />
    </div>
  );
};

StandardMixedChart.propTypes = {
  labels: PropTypes.arrayOf(PropTypes.string).isRequired,
  datasets: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      data: PropTypes.arrayOf(PropTypes.number).isRequired,
      type: PropTypes.oneOf(['bar', 'line']),
      yAxisID: PropTypes.string,
      backgroundColor: PropTypes.string,
      borderColor: PropTypes.string,
      borderWidth: PropTypes.number,
      tension: PropTypes.number,
      fill: PropTypes.bool,
    }),
  ).isRequired,
  title: PropTypes.string,
  showDataLabels: PropTypes.bool,
  showLegend: PropTypes.bool,
  showGrid: PropTypes.bool,
  leftAxisConfig: PropTypes.object,
  rightAxisConfig: PropTypes.object,
  xAxisConfig: PropTypes.object,
  height: PropTypes.string,
  width: PropTypes.string,
  responsive: PropTypes.bool,
  customOptions: PropTypes.object,
};

export default StandardMixedChart;
