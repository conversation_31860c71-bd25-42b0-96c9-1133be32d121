import PropTypes from 'prop-types';

import { Line } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Chart.js configuration
import ChartJS from '../../../utils/chartConfig';

// Material Dashboard 2 React Base Styles
import colors from 'assets/theme/base/colors';
import typography from 'assets/theme/base/typography';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

const { gradients, dark } = colors;
const { fontFamily, fontWeightRegular, fontWeightMedium, fontWeightBold } = typography;

/**
 * StandardLineChart - 标准化折线图组件
 * @param {Object} props
 * @param {Array} props.labels - X轴标签数组
 * @param {Array} props.datasets - 数据集数组，每个数据集包含 { label, data, borderColor?, backgroundColor?, fill?, tension? }
 * @param {String} props.title - 图表标题
 * @param {Boolean} props.showDataLabels - 是否显示数据标签
 * @param {Boolean} props.showLegend - 是否显示图例
 * @param {Boolean} props.showGrid - 是否显示网格
 * @param {Boolean} props.smooth - 是否平滑曲线
 * @param {Boolean} props.fill - 是否填充区域
 * @param {String} props.timeFormat - 时间格式（用于时间序列）
 * @param {Object} props.yAxisConfig - Y轴配置
 * @param {Object} props.xAxisConfig - X轴配置
 * @param {String} props.height - 图表高度
 * @param {String} props.width - 图表宽度
 * @param {Boolean} props.responsive - 是否响应式
 * @param {Object} props.customOptions - 自定义配置选项
 * @returns {JSX.Element}
 */
const StandardLineChart = ({
  labels,
  datasets,
  title,
  showDataLabels = false,
  showLegend = true,
  showGrid = true,
  smooth = true,
  fill = false,
  timeFormat,
  yAxisConfig = {},
  xAxisConfig = {},
  height = '400px',
  width = '100%',
  responsive = true,
  customOptions = {},
}) => {
  // 默认颜色配置
  const defaultColors = [
    gradients.info.main,
    gradients.success.main,
    gradients.warning.main,
    gradients.error.main,
    gradients.primary.main,
    gradients.secondary.main,
    gradients.dark.main,
  ];

  // 处理数据集，添加默认样式
  const processedDatasets = datasets.map((dataset, index) => ({
    ...dataset,
    borderColor: dataset.borderColor || defaultColors[index % defaultColors.length],
    backgroundColor:
      dataset.backgroundColor ||
      (fill ? `${defaultColors[index % defaultColors.length]}20` : 'transparent'),
    borderWidth: dataset.borderWidth || 2,
    tension: dataset.tension !== undefined ? dataset.tension : smooth ? 0.4 : 0,
    fill: dataset.fill !== undefined ? dataset.fill : fill,
    pointRadius: dataset.pointRadius !== undefined ? dataset.pointRadius : 4,
    pointHoverRadius: dataset.pointHoverRadius !== undefined ? dataset.pointHoverRadius : 6,
    pointBackgroundColor:
      dataset.pointBackgroundColor ||
      dataset.borderColor ||
      defaultColors[index % defaultColors.length],
    pointBorderColor: dataset.pointBorderColor || '#ffffff',
    pointBorderWidth: dataset.pointBorderWidth || 2,
    pointHoverBackgroundColor:
      dataset.pointHoverBackgroundColor ||
      dataset.borderColor ||
      defaultColors[index % defaultColors.length],
    pointHoverBorderColor: dataset.pointHoverBorderColor || '#ffffff',
    pointHoverBorderWidth: dataset.pointHoverBorderWidth || 2,
  }));

  // 图表数据
  const chartData = {
    labels,
    datasets: processedDatasets,
  };

  // 默认配置选项
  const defaultOptions = {
    responsive,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    plugins: {
      legend: {
        display: showLegend,
        position: 'top',
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightRegular,
          },
          color: dark.main,
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          family: fontFamily,
          size: 16,
          weight: fontWeightBold,
        },
        color: dark.main,
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          family: fontFamily,
          size: 14,
          weight: fontWeightMedium,
        },
        bodyFont: {
          family: fontFamily,
          size: 12,
          weight: fontWeightRegular,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            return `${context.dataset.label}: ${context.parsed.y?.toLocaleString()}`;
          },
        },
      },
      datalabels: {
        display: showDataLabels,
        anchor: 'end',
        align: 'top',
        offset: 4,
        color: dark.main,
        font: {
          family: fontFamily,
          size: 10,
          weight: fontWeightMedium,
        },
        formatter: value => {
          return typeof value === 'number' ? value.toLocaleString() : value;
        },
        clip: true,
      },
    },
    scales: {
      x: {
        display: true,
        type: timeFormat ? 'time' : 'category',
        time: timeFormat
          ? {
            displayFormats: {
              day: timeFormat,
              week: timeFormat,
              month: timeFormat,
              year: timeFormat,
            },
          }
          : undefined,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1,
          drawBorder: true,
          drawTicks: true,
        },
        ticks: {
          color: dark.main,
          font: {
            family: fontFamily,
            size: 11,
            weight: fontWeightRegular,
          },
          maxRotation: labels && labels.length > 8 ? 45 : 0,
          minRotation: labels && labels.length > 8 ? 45 : 0,
          padding: 8,
        },
        title: {
          display: !!xAxisConfig.title,
          text: xAxisConfig.title,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightMedium,
          },
          color: dark.main,
        },
        ...xAxisConfig,
      },
      y: {
        display: true,
        grid: {
          display: showGrid,
          color: 'rgba(0, 0, 0, 0.1)',
          lineWidth: 1,
          drawBorder: true,
          drawTicks: true,
        },
        ticks: {
          color: dark.main,
          font: {
            family: fontFamily,
            size: 11,
            weight: fontWeightRegular,
          },
          padding: 8,
          callback: function (value) {
            return typeof value === 'number' ? value.toLocaleString() : value;
          },
        },
        title: {
          display: !!yAxisConfig.title,
          text: yAxisConfig.title,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightMedium,
          },
          color: dark.main,
        },
        beginAtZero: true,
        ...yAxisConfig,
      },
    },
    elements: {
      line: {
        borderJoinStyle: 'round',
      },
      point: {
        hoverRadius: 8,
      },
    },
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20,
      },
    },
  };

  // 合并自定义选项
  const finalOptions = {
    ...defaultOptions,
    ...customOptions,
    plugins: {
      ...defaultOptions.plugins,
      ...customOptions.plugins,
    },
    scales: {
      ...defaultOptions.scales,
      ...customOptions.scales,
    },
  };

  return (
    <div
      style={{
        width,
        height,
        position: 'relative',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        padding: '16px',
        overflow: 'hidden',
      }}
    >
      <Line data={chartData} options={finalOptions} />
    </div>
  );
};

StandardLineChart.propTypes = {
  labels: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])).isRequired,
  datasets: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      data: PropTypes.arrayOf(PropTypes.number).isRequired,
      borderColor: PropTypes.string,
      backgroundColor: PropTypes.string,
      fill: PropTypes.bool,
      tension: PropTypes.number,
    }),
  ).isRequired,
  title: PropTypes.string,
  showDataLabels: PropTypes.bool,
  showLegend: PropTypes.bool,
  showGrid: PropTypes.bool,
  smooth: PropTypes.bool,
  fill: PropTypes.bool,
  timeFormat: PropTypes.string,
  yAxisConfig: PropTypes.object,
  xAxisConfig: PropTypes.object,
  height: PropTypes.string,
  width: PropTypes.string,
  responsive: PropTypes.bool,
  customOptions: PropTypes.object,
};

export default StandardLineChart;
