import PropTypes from 'prop-types';
import { Pie, Doughnut } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';

// Chart.js configuration
import ChartJS from '../../../utils/chartConfig';

// Material Dashboard 2 React Base Styles
import colors from 'assets/theme/base/colors';
import typography from 'assets/theme/base/typography';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

const { gradients, info, success, warning, error, primary, secondary, dark } = colors;
const { fontFamily, fontWeightRegular, fontWeightMedium, fontWeightBold } = typography;

/**
 * StandardPieChart - 标准化饼图/环形图组件
 * @param {Object} props
 * @param {Array} props.labels - 标签数组
 * @param {Array} props.data - 数据数组
 * @param {String} props.title - 图表标题
 * @param {String} props.type - 图表类型 'pie' | 'doughnut'
 * @param {Boolean} props.showDataLabels - 是否显示数据标签
 * @param {Boolean} props.showLegend - 是否显示图例
 * @param {Boolean} props.showPercentage - 是否显示百分比
 * @param {Array} props.colors - 自定义颜色数组
 * @param {String} props.legendPosition - 图例位置 'top' | 'bottom' | 'left' | 'right'
 * @param {Number} props.cutout - 环形图内圆半径百分比（仅用于 doughnut 类型）
 * @param {String} props.height - 图表高度
 * @param {String} props.width - 图表宽度
 * @param {Boolean} props.responsive - 是否响应式
 * @param {Object} props.customOptions - 自定义配置选项
 * @returns {JSX.Element}
 */
const StandardPieChart = ({
  labels,
  data,
  title,
  type = 'pie',
  showDataLabels = true,
  showLegend = true,
  showPercentage = true,
  colors: customColors,
  legendPosition = 'top',
  cutout = 50,
  height = '400px',
  width = '100%',
  responsive = true,
  customOptions = {},
}) => {
  // 默认颜色配置
  const defaultColors = [
    gradients.info.main,
    gradients.success.main,
    gradients.warning.main,
    gradients.error.main,
    gradients.primary.main,
    gradients.secondary.main,
    gradients.dark.main,
    info.main,
    success.main,
    warning.main,
    error.main,
    primary.main,
    secondary.main,
  ];

  // 使用自定义颜色或默认颜色
  const chartColors = customColors || defaultColors;

  // 计算百分比
  const total = data.reduce((sum, value) => sum + value, 0);
  const percentages = data.map(value => ((value / total) * 100).toFixed(1));

  // 图表数据
  const chartData = {
    labels,
    datasets: [
      {
        data,
        backgroundColor: chartColors.slice(0, data.length),
        borderColor: chartColors
          .slice(0, data.length)
          .map(color => color.replace(/[^,]+(?=\))/, '1')),
        borderWidth: 2,
        hoverOffset: 4,
      },
    ],
  };

  // 默认配置选项
  const defaultOptions = {
    responsive,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: showLegend,
        position: legendPosition,
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            family: fontFamily,
            size: 12,
            weight: fontWeightRegular,
          },
          color: dark.main,
          generateLabels: function (chart) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              const dataset = data.datasets[0];
              return data.labels.map((label, index) => {
                const value = dataset.data[index];
                const percentage = percentages[index];
                return {
                  text: showPercentage ? `${label} (${percentage}%)` : label,
                  fillStyle: dataset.backgroundColor[index],
                  strokeStyle: dataset.borderColor[index],
                  lineWidth: dataset.borderWidth,
                  hidden: isNaN(value) || value === 0,
                  index,
                };
              });
            }
            return [];
          },
        },
      },
      title: {
        display: !!title,
        text: title,
        font: {
          family: fontFamily,
          size: 16,
          weight: fontWeightBold,
        },
        color: dark.main,
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          family: fontFamily,
          size: 14,
          weight: fontWeightMedium,
        },
        bodyFont: {
          family: fontFamily,
          size: 12,
          weight: fontWeightRegular,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value.toLocaleString()} (${percentage}%)`;
          },
        },
      },
      datalabels: {
        display: showDataLabels,
        color: '#ffffff',
        font: {
          family: fontFamily,
          size: 12,
          weight: fontWeightBold,
        },
        formatter: value => {
          const percentage = ((value / total) * 100).toFixed(1);
          if (showPercentage) {
            return percentage < 5 ? '' : `${percentage}%`; // 不显示小于5%的标签
          }
          return value.toLocaleString();
        },
        textAlign: 'center',
        anchor: 'center',
        align: 'center',
        offset: 0,
        clip: true,
      },
    },
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20,
      },
    },
    // 环形图特定配置
    ...(type === 'doughnut' && {
      cutout: `${cutout}%`,
    }),
  };

  // 合并自定义选项
  const finalOptions = {
    ...defaultOptions,
    ...customOptions,
    plugins: {
      ...defaultOptions.plugins,
      ...customOptions.plugins,
    },
  };

  // 选择图表组件

  const ChartComponent = type === 'doughnut' ? Doughnut : Pie;

  return (
    <div
      style={{
        width,
        height,
        position: 'relative',
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        padding: '16px',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <ChartComponent data={chartData} options={finalOptions} />

      {/* 环形图中心文本 */}
      {type === 'doughnut' && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            pointerEvents: 'none',
          }}
        >
          <div
            style={{
              fontSize: '24px',
              fontWeight: fontWeightBold,
              color: dark.main,
              fontFamily,
            }}
          >
            {total.toLocaleString()}
          </div>
          <div
            style={{
              fontSize: '12px',
              color: colors.text.main,
              fontFamily,
              marginTop: '4px',
            }}
          >
            总计
          </div>
        </div>
      )}
    </div>
  );
};

StandardPieChart.propTypes = {
  labels: PropTypes.arrayOf(PropTypes.string).isRequired,
  data: PropTypes.arrayOf(PropTypes.number).isRequired,
  title: PropTypes.string,
  type: PropTypes.oneOf(['pie', 'doughnut']),
  showDataLabels: PropTypes.bool,
  showLegend: PropTypes.bool,
  showPercentage: PropTypes.bool,
  colors: PropTypes.arrayOf(PropTypes.string),
  legendPosition: PropTypes.oneOf(['top', 'bottom', 'left', 'right']),
  cutout: PropTypes.number,
  height: PropTypes.string,
  width: PropTypes.string,
  responsive: PropTypes.bool,
  customOptions: PropTypes.object,
};

export default StandardPieChart;
