import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Paper, Typography, IconButton } from '@mui/material';
import { CalendarToday, ChevronLeft, ChevronRight } from '@mui/icons-material';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
  zIndex,
} from '../../constants/styleConstants';

const FormMonthPicker = ({
  label,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  placeholder = '请选择月份',
  name,
  // Date validation
  minDate,
  maxDate,
  // Style options
  fullWidth = true,
  size = 'medium',
  // Callbacks
  onFocus,
  onBlur,
  // Additional props
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewYear, setViewYear] = useState(
    value ? new Date(value + '-01').getFullYear() : new Date().getFullYear(),
  );
  const [selectedMonth, setSelectedMonth] = useState(value || '');

  const monthPickerRef = useRef(null);

  // Handle click outside to close picker
  useEffect(() => {
    const handleClickOutside = event => {
      if (monthPickerRef.current && !monthPickerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Format month for display
  const formatMonthForDisplay = monthValue => {
    if (!monthValue) {
      return '';
    }
    const [year, month] = monthValue.split('-');
    return `${year}年${parseInt(month)}月`;
  };

  // Check if month is valid
  const isValidMonth = monthValue => {
    if (!monthValue) {
      return true;
    }

    if (minDate && monthValue < minDate) {
      return false;
    }
    if (maxDate && monthValue > maxDate) {
      return false;
    }

    return true;
  };

  // Handle month selection
  const handleMonthSelect = (year, month) => {
    const monthValue = `${year}-${String(month + 1).padStart(2, '0')}`;

    if (isValidMonth(monthValue)) {
      setSelectedMonth(monthValue);

      const mockEvent = {
        target: { name, value: monthValue },
      };
      onChange(mockEvent);
      setIsOpen(false);
    }
  };

  // Navigate years
  const navigateYear = direction => {
    setViewYear(prevYear => prevYear + direction);
  };

  const handleToggle = () => {
    if (disabled) {
      return;
    }

    setIsOpen(!isOpen);
    if (!isOpen && onFocus) {
      onFocus({ target: { name, value } });
    }
    if (isOpen && onBlur) {
      onBlur({ target: { name, value } });
    }
  };

  const getInputStyles = () => ({
    width: fullWidth ? '100%' : 'auto',
    padding: size === 'small' ? `${spacing.xs} ${spacing.sm}` : `${spacing.sm} ${spacing.md}`,
    borderRadius: dimensions.borderRadius.base,
    border: `${dimensions.borderWidth.base} solid ${
      error ? colors.error.main : colors.border.main
    }`,
    fontSize: size === 'small' ? typography.fontSize.xs : typography.fontSize.sm,
    fontFamily: typography.fontFamily.base,
    height: size === 'small' ? '28px' : dimensions.height.input,
    boxSizing: 'border-box',
    backgroundColor: disabled ? colors.background.light : colors.background.paper,
    color: disabled ? colors.text.disabled : colors.text.primary,
    cursor: disabled ? 'not-allowed' : 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    transition: transitions.all,
    opacity: disabled ? 0.6 : 1,
    boxShadow: error ? `0 0 0 1px ${colors.error.main}` : 'none',

    '&:hover': {
      borderColor: disabled ? colors.border.main : error ? colors.error.dark : colors.border.dark,
    },

    '&:focus': {
      outline: 'none',
      borderColor: error ? colors.error.main : colors.primary.main,
      boxShadow: error ? `0 0 0 1px ${colors.error.main}` : `0 0 0 1px ${colors.primary.main}`,
    },
  });

  const getPickerStyles = () => ({
    position: 'absolute',
    top: '100%',
    left: 0,
    zIndex: zIndex.dropdown,
    marginTop: '2px',
    minWidth: '280px',
    backgroundColor: colors.background.paper,
    boxShadow: shadows.dropdown,
    borderRadius: dimensions.borderRadius.base,
    border: `${dimensions.borderWidth.base} solid ${colors.border.light}`,
    padding: spacing.md,
  });

  const getMonthStyles = (year, month) => {
    const monthValue = `${year}-${String(month + 1).padStart(2, '0')}`;
    const isSelected = monthValue === selectedMonth;
    const isCurrentMonth = new Date().getFullYear() === year && new Date().getMonth() === month;
    const isDisabled = !isValidMonth(monthValue);

    return {
      width: '80px',
      height: '40px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: isDisabled ? 'not-allowed' : 'pointer',
      fontSize: typography.fontSize.sm,
      borderRadius: dimensions.borderRadius.base,
      transition: transitions.all,
      backgroundColor: isSelected
        ? colors.primary.main
        : isCurrentMonth
          ? colors.primary.light
          : 'transparent',
      color: isSelected
        ? colors.primary.contrastText
        : isCurrentMonth
          ? colors.primary.main
          : colors.text.primary,
      opacity: isDisabled ? 0.4 : 1,
      margin: '4px',

      '&:hover': {
        backgroundColor: isSelected
          ? colors.primary.dark
          : isDisabled
            ? 'transparent'
            : colors.background.hover,
      },
    };
  };

  const getLabelStyles = () => ({
    display: 'block',
    marginBottom: spacing.xs,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: error ? colors.error.main : colors.text.primary,
    fontFamily: typography.fontFamily.base,
  });

  const months = [
    '1月',
    '2月',
    '3月',
    '4月',
    '5月',
    '6月',
    '7月',
    '8月',
    '9月',
    '10月',
    '11月',
    '12月',
  ];

  return (
    <Box sx={{ marginBottom: spacing.md, position: 'relative' }} ref={monthPickerRef}>
      <Typography variant="body2" component="label" sx={getLabelStyles()}>
        {label}
        {required && <span style={{ color: colors.error.main, marginLeft: '2px' }}>*</span>}
      </Typography>

      <Box
        onClick={handleToggle}
        role="button"
        aria-expanded={isOpen}
        aria-disabled={disabled}
        tabIndex={disabled ? -1 : 0}
        sx={getInputStyles()}
        {...props}
      >
        <span style={{ flex: 1, textAlign: 'left' }}>
          {formatMonthForDisplay(selectedMonth) || placeholder}
        </span>
        <Box sx={{ marginLeft: spacing.sm, display: 'flex', alignItems: 'center' }}>
          <CalendarToday />
        </Box>
      </Box>

      {isOpen && !disabled && (
        <Paper sx={getPickerStyles()}>
          {/* Year Header */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: spacing.md,
            }}
          >
            <IconButton onClick={() => navigateYear(-1)} size="small">
              <ChevronLeft />
            </IconButton>
            <Typography variant="subtitle1" sx={{ fontWeight: typography.fontWeight.medium }}>
              {viewYear}年
            </Typography>
            <IconButton onClick={() => navigateYear(1)} size="small">
              <ChevronRight />
            </IconButton>
          </Box>

          {/* Month Grid */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: spacing.xs,
            }}
          >
            {months.map((monthName, index) => (
              <Box
                key={index}
                onClick={() => handleMonthSelect(viewYear, index)}
                sx={getMonthStyles(viewYear, index)}
              >
                {monthName}
              </Box>
            ))}
          </Box>
        </Paper>
      )}

      {error && (
        <Typography
          variant="caption"
          sx={{
            color: colors.error.main,
            display: 'block',
            marginTop: spacing.xs,
            fontSize: typography.fontSize.xs,
          }}
        >
          {error}
        </Typography>
      )}
    </Box>
  );
};

FormMonthPicker.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  // Date validation
  minDate: PropTypes.string,
  maxDate: PropTypes.string,
  // Style options
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
  // Callbacks
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
};

export default FormMonthPicker;
