/**
 * Form Components Index
 *
 * This file exports all standardized form components for the FinancialSystem
 * application. These components are designed to be consistent, reusable, and
 * follow the established design patterns.
 */

import FormInput from './FormInput';
import FormSelect from './FormSelect';
import FormDatePicker from './FormDatePicker';
import FormMonthPicker from './FormMonthPicker';

// Export individual components
export { FormInput, FormSelect, FormDatePicker, FormMonthPicker };

// Export as default object for convenience
const FormComponents = {
  FormInput,
  FormSelect,
  FormDatePicker,
  FormMonthPicker,
};

export default FormComponents;

/**
 * Component Usage Examples:
 *
 * // Basic Text Input
 * <FormInput
 *   label="客户名称"
 *   value={formData.customerName}
 *   onChange={handleChange}
 *   required
 *   name="customerName"
 *   placeholder="请输入客户名称"
 * />
 *
 * // Number Input with Validation
 * <FormInput
 *   label="债权金额"
 *   value={formData.amount}
 *   onChange={handleChange}
 *   type="number"
 *   required
 *   name="amount"
 *   isNumberFormat
 *   decimalPlaces={2}
 *   minValue={0}
 *   placeholder="请输入债权金额"
 * />
 *
 * // Email Input
 * <FormInput
 *   label="邮箱地址"
 *   value={formData.email}
 *   onChange={handleChange}
 *   type="email"
 *   name="email"
 *   placeholder="请输入邮箱地址"
 * />
 *
 * // Select Dropdown
 * <FormSelect
 *   label="债权类型"
 *   value={formData.debtType}
 *   onChange={handleChange}
 *   required
 *   name="debtType"
 *   options={[
 *     { value: 'loan', label: '贷款' },
 *     { value: 'credit', label: '信用债' },
 *     { value: 'other', label: '其他' },
 *   ]}
 * />
 *
 * // Multi-Select with Search
 * <FormSelect
 *   label="关联公司"
 *   value={formData.relatedCompanies}
 *   onChange={handleChange}
 *   name="relatedCompanies"
 *   multiple
 *   searchable
 *   options={companyOptions}
 * />
 *
 * // Date Picker
 * <FormDatePicker
 *   label="债权发生日期"
 *   value={formData.debtDate}
 *   onChange={handleChange}
 *   required
 *   name="debtDate"
 *   mode="date"
 * />
 *
 * // DateTime Picker
 * <FormDatePicker
 *   label="处置时间"
 *   value={formData.disposalDateTime}
 *   onChange={handleChange}
 *   name="disposalDateTime"
 *   mode="datetime"
 * />
 */
