import React, { useEffect, useState } from 'react';

/**
 =========================================================
 * Material Dashboard 2 React - v2.2.0
 =========================================================

 * Product Page: https://www.creative-tim.com/product/material-dashboard-react
 * Copyright 2023 Creative Tim (https://www.creative-tim.com)

 Coded by www.creative-tim.com

 =========================================================

 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
 */

// react-router-dom components
import { useLocation, NavLink } from 'react-router-dom';

// prop-types is a library for typechecking of props.
import PropTypes from 'prop-types';

// @mui material components
import List from '@mui/material/List';
import Divider from '@mui/material/Divider';
import Link from '@mui/material/Link';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// Material Dashboard 2 React example components
import SidenavCollapse from 'examples/Sidenav/SidenavCollapse';

// Custom styles for the Sidenav
import SidenavRoot from 'examples/Sidenav/SidenavRoot';
import sidenavLogoLabel from 'examples/Sidenav/styles/sidenav';

// Material Dashboard 2 React context
import {
  useMaterialUIController,
  setMiniSidenav,
  setTransparentSidenav,
  setWhiteSidenav,
} from 'context';

function Sidenav({ color, brand, brandName, routes, ...rest }) {
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, transparentSidenav, whiteSidenav, darkMode } = controller;
  const location = useLocation();
  const collapseName = location.pathname.replace('/', '').replace(/\//g, '-');

  // 添加状态来跟踪哪些菜单项是展开的
  const [expandedItems, setExpandedItems] = useState({});

  let textColor = 'white';
  if (transparentSidenav || (whiteSidenav && !darkMode)) {
    textColor = 'dark';
  } else if (whiteSidenav && darkMode) {
    textColor = 'inherit';
  }

  // 侧边栏最小化切换
  const closeSidenav = () => setMiniSidenav(dispatch, true);

  useEffect(() => {
    function handleMiniSidenav() {
      setMiniSidenav(dispatch, window.innerWidth < 1200);
      setTransparentSidenav(dispatch, window.innerWidth < 1200 ? false : transparentSidenav);
      setWhiteSidenav(dispatch, window.innerWidth < 1200 ? false : whiteSidenav);
    }

    window.addEventListener('resize', handleMiniSidenav);
    handleMiniSidenav();
    return () => window.removeEventListener('resize', handleMiniSidenav);
  }, [dispatch, location, transparentSidenav, whiteSidenav]);

  /**
   * 递归渲染多级菜单
   * @param {Array} routes - 路由配置数组
   */
  function renderNestedRoutes(routes = []) {
    return routes.map(({ type, name, icon, title, noCollapse, key, href, route, collapse }) => {
      // 定义一个局部变量存储单个菜单项的 JSX
      let returnValue = null;

      // 1) 可折叠（或带子菜单）的项
      if (type === 'collapse') {
        // 检查这个菜单项是否在已折叠状态中
        const isExpanded = expandedItems[key] || false;

        // 处理父级菜单项的点击事件
        const handleCollapseClick = e => {
          // 只有当有子菜单时才阻止导航
          if (collapse && collapse.length) {
            e.preventDefault();
            // 切换此菜单项的折叠状态
            setExpandedItems(prev => ({
              ...prev,
              [key]: !prev[key],
            }));
          }
        };

        // 父级项：如果有外链 href，就用 <Link> 包裹，否则用 <NavLink>
        const parentItem = href ? (
          <Link
            href={href}
            key={key}
            target="_blank"
            rel="noreferrer"
            sx={{ textDecoration: 'none' }}
          >
            <SidenavCollapse
              name={name}
              icon={icon}
              active={key === collapseName}
              noCollapse={noCollapse}
            />
          </Link>
        ) : (
          <NavLink
            key={key}
            to={route || '#'}
            onClick={handleCollapseClick}
            style={{ textDecoration: 'none' }}
          >
            <SidenavCollapse
              name={name}
              icon={icon}
              active={key === collapseName}
              // 添加展开/折叠指示图标
              rightIcon={
                collapse && collapse.length ? (
                  <Icon
                    sx={{
                      ml: 1,
                      fontSize: '1rem',
                      transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.3s',
                    }}
                  >
                    expand_more
                  </Icon>
                ) : null
              }
            />
          </NavLink>
        );

        // 如果有子菜单 collapse 数组，递归生成子菜单
        let childItems = null;
        if (collapse && collapse.length) {
          childItems = (
            <MDBox
              ml={3}
              key={`${key}-children`}
              sx={{
                height: isExpanded ? 'auto' : '0px',
                overflow: 'hidden',
                transition: 'height 0.3s ease',
                opacity: isExpanded ? 1 : 0,
                display: isExpanded ? 'block' : 'none',
              }}
            >
              {renderNestedRoutes(collapse)}
            </MDBox>
          );
        }

        returnValue = (
          <MDBox key={key}>
            {parentItem}
            {childItems}
          </MDBox>
        );
      } else if (type === 'title') {
        returnValue = (
          <MDTypography
            key={key}
            color={textColor}
            display="block"
            variant="caption"
            fontWeight="bold"
            textTransform="uppercase"
            pl={3}
            mt={2}
            mb={1}
            ml={1}
          >
            {title}
          </MDTypography>
        );
      } else if (type === 'divider') {
        returnValue = (
          <Divider
            key={key}
            light={
              (!darkMode && !whiteSidenav && !transparentSidenav) ||
              (darkMode && !transparentSidenav && whiteSidenav)
            }
          />
        );
      }

      return returnValue;
    });
  }

  // 递归渲染所有菜单
  const renderRoutes = renderNestedRoutes(routes);

  return (
    <SidenavRoot
      {...rest}
      variant="permanent"
      ownerState={{ transparentSidenav, whiteSidenav, miniSidenav, darkMode }}
    >
      {/* 顶部品牌区域 */}
      <MDBox pt={3} pb={1} px={4} textAlign="center">
        <MDBox
          display={{ xs: 'block', xl: 'none' }}
          position="absolute"
          top={0}
          right={0}
          p={1.625}
          onClick={closeSidenav}
          sx={{ cursor: 'pointer' }}
        >
          <MDTypography variant="h6" color="secondary">
            <Icon sx={{ fontWeight: 'bold' }}>close</Icon>
          </MDTypography>
        </MDBox>
        <MDBox component={NavLink} to="/" display="flex" alignItems="center">
          {brand && <MDBox component="img" src={brand} alt="Brand" width="2rem" />}
          <MDBox
            width={!brandName && '100%'}
            sx={theme => sidenavLogoLabel(theme, { miniSidenav })}
          >
            <MDTypography component="h6" variant="button" fontWeight="medium" color={textColor}>
              {brandName}
            </MDTypography>
          </MDBox>
        </MDBox>
      </MDBox>

      {/* 顶部与菜单之间的分割线 */}
      <Divider
        light={
          (!darkMode && !whiteSidenav && !transparentSidenav) ||
          (darkMode && !transparentSidenav && whiteSidenav)
        }
      />

      {/* 核心：递归渲染菜单 */}
      <List>{renderRoutes}</List>

      {/* 底部按钮示例，可自行删除或修改 */}
      {/*<MDBox p={2} mt="auto">*/}
      {/*  <MDButton*/}
      {/*    component="a"*/}
      {/*    href="https://www.creative-tim.com/product/material-dashboard-pro-react"*/}
      {/*    target="_blank"*/}
      {/*    rel="noreferrer"*/}
      {/*    variant="gradient"*/}
      {/*    color={sidenavColor}*/}
      {/*    fullWidth*/}
      {/*  >*/}
      {/*    upgrade to pro*/}
      {/*  </MDButton>*/}
      {/*</MDBox>*/}
    </SidenavRoot>
  );
}

// Setting default values for the props of Sidenav
Sidenav.defaultProps = {
  color: 'info',
  brand: '',
};

// Typechecking props for the Sidenav
Sidenav.propTypes = {
  color: PropTypes.oneOf(['primary', 'secondary', 'info', 'success', 'warning', 'error', 'dark']),
  brand: PropTypes.string,
  brandName: PropTypes.string.isRequired,
  routes: PropTypes.arrayOf(PropTypes.object).isRequired,
};

export default Sidenav;
