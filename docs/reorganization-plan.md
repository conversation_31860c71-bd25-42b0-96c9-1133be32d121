# 文档重组计划

## 📊 现状分析

当前docs目录有120个markdown文件，分布在24个目录中。存在以下问题：
1. **结构冗余**：许多目录内容重叠（如analysis、reports、planning）
2. **过时文档**：大量历史文档未归档
3. **分类不清**：同类文档分散在不同目录
4. **缺少自动化**：无文档整理自动化机制

## 🎯 新文档结构设计

```
docs/
├── README.md                    # 文档中心导航
├── CHANGELOG.md                 # 项目变更日志
│
├── 01-quickstart/              # 快速开始（新人必看）
│   ├── README.md               # 快速入门指南
│   ├── setup.md                # 环境搭建
│   └── first-steps.md          # 第一步操作
│
├── 02-architecture/            # 架构设计
│   ├── README.md               # 架构概览
│   ├── system-design.md        # 系统设计
│   ├── database-schema.md      # 数据库设计
│   └── api-design.md           # API设计原则
│
├── 03-development/             # 开发文档（保持不动）
│   ├── README.md
│   ├── workflow-commands-guide.md
│   ├── commands-cheatsheet.md
│   └── ... (其他现有文件)
│
├── 04-business/                # 业务文档
│   ├── README.md               # 业务概览
│   ├── debt-management/        # 债权管理
│   ├── user-system/            # 用户系统
│   └── reporting/              # 报表系统
│
├── 05-operations/              # 运维文档
│   ├── README.md               # 运维指南
│   ├── deployment/             # 部署相关
│   ├── monitoring/             # 监控相关
│   └── maintenance/            # 维护相关
│
├── 06-api/                     # API文档
│   ├── README.md               # API概览
│   ├── endpoints/              # 端点文档
│   └── examples/               # 使用示例
│
├── 07-troubleshooting/         # 故障排查
│   ├── README.md               # 问题索引
│   ├── common-issues.md        # 常见问题
│   └── solutions/              # 解决方案
│
└── archive/                    # 归档文档
    ├── README.md               # 归档说明
    ├── 2024/                   # 按年份归档
    └── 2025/
```

## 📝 文档迁移映射

### 需要迁移的文档

| 原位置 | 新位置 | 说明 |
|--------|--------|------|
| guides/* | 01-quickstart/ | 整合为快速开始指南 |
| architecture/* | 02-architecture/ | 保持架构文档集中 |
| database/* | 02-architecture/database-schema.md | 合并数据库文档 |
| business/* | 04-business/ | 按业务模块重组 |
| deployment/* | 05-operations/deployment/ | 归入运维文档 |
| infrastructure/* | 05-operations/ | 归入运维文档 |
| api/* | 06-api/ | 保持API文档独立 |
| troubleshooting/* | 07-troubleshooting/ | 保持故障排查独立 |

### 需要归档的文档

| 文档类型 | 归档原因 | 目标位置 |
|----------|----------|----------|
| analysis/* | 已完成的分析文档 | archive/2025/analysis/ |
| reports/* | 历史报告 | archive/2025/reports/ |
| planning/* | 已完成的计划 | archive/2025/planning/ |
| bug-fixes/* | 已修复的问题 | archive/2025/bug-fixes/ |
| features/* | 已实现的功能 | archive/2025/features/ |
| 项目结构整理相关 | 已完成 | archive/2025/restructuring/ |

### 需要删除的文档

1. 重复的文档（保留最新版本）
2. 空文件或模板文件
3. 临时文档和草稿

## 🔧 实施步骤

### Phase 1: 准备工作
1. 备份当前docs目录
2. 创建新的目录结构
3. 创建文档整理脚本

### Phase 2: 文档迁移
1. 迁移核心文档到新结构
2. 整合重复内容
3. 更新文档内的相对链接

### Phase 3: 归档处理
1. 将历史文档移到archive
2. 创建归档索引
3. 保留重要历史记录

### Phase 4: 清理优化
1. 删除冗余文档
2. 更新主README.md
3. 验证所有链接

### Phase 5: 自动化设置
1. 创建文档整理脚本
2. 添加到cleanup命令
3. 设置定期执行计划

## 🤖 自动化方案

### 1. 文档整理脚本
创建 `scripts/organize-docs.sh`：
- 自动检测过期文档（超过6个月未更新）
- 自动归档已完成的项目文档
- 检查并修复断链
- 生成文档更新报告

### 2. Git Hooks集成
在 `.git/hooks/pre-commit` 添加：
- 检查新增文档是否符合命名规范
- 验证文档放置位置是否正确
- 更新文档索引

### 3. 定期维护
建议每月执行一次文档整理：
```bash
# 添加到crontab或手动执行
./scripts/organize-docs.sh --monthly-cleanup
```

## 📋 文档标准

### 命名规范
- 使用小写字母和连字符：`feature-name.md`
- 避免中文文件名（除业务文档外）
- 日期格式：`YYYY-MM-DD-title.md`

### 内容规范
- 每个目录必须有README.md
- 文档开头包含更新日期
- 使用相对路径链接
- 保持markdown格式一致

### 归档规则
- 项目完成后归档相关文档
- 超过6个月未更新考虑归档
- 保留最近2年的归档文档
- 更早的可以压缩存储

## 🚀 预期效果

1. **结构清晰**：7个主要分类，易于导航
2. **维护简单**：自动化整理，减少手动工作
3. **查找快速**：合理分类，快速定位
4. **历史可追溯**：完整的归档体系

## ⏰ 执行时间表

- **即时执行**：创建新目录结构
- **1-2小时**：完成文档迁移
- **30分钟**：更新链接和索引
- **未来**：每月自动执行整理

---
*文档重组计划 - 2025-01-31*