# 故障排除指南

本目录包含FinancialSystem项目的常见问题、解决方案和问题修复记录。

## 📋 常见问题分类

### 🚀 启动问题
- **问题**: 应用启动失败
- **解决方案**: 
  1. 检查Java版本是否为21
  2. 检查数据库连接配置
  3. 检查端口8080是否被占用
  4. 查看启动日志中的错误信息

### 🗄️ 数据库问题
- **问题**: 数据库连接失败
- **解决方案**:
  1. 检查MySQL服务是否启动
  2. 验证数据库用户名密码
  3. 确认数据库名称正确
  4. 检查网络连接

### 🐳 Docker部署问题
- **问题**: Docker容器启动失败
- **解决方案**:
  1. 检查docker-compose.yml配置
  2. 查看容器日志: `docker-compose logs`
  3. 检查端口映射冲突
  4. 验证镜像是否正确构建

### 🔧 编译问题
- **问题**: Maven编译失败
- **解决方案**:
  1. 清理缓存: `mvn clean`
  2. 检查依赖版本冲突
  3. 验证模块路径配置
  4. 检查Java版本兼容性

## 📋 问题修复记录

### 已解决的问题
- [债权删除问题修复](debt-deletion-issues-resolved.md) - 债权删除功能相关问题的修复记录
- [减值准备字段映射修复](impairment-reserve-field-mapping-fix.md) - 减值准备表字段映射问题修复
- [债权新增公司选择修复](debt-add-company-selection-fix.md) - 债权新增时公司选择功能修复
- [债权删除双重扣减修复](debt-deletion-double-deduction-fix.md) - 债权删除时双重扣减问题修复
- [债权减值500错误修复](debt-reduction-500-error-fix.md) - 债权减值操作500错误修复
- [非诉讼字段映射修复](non-litigation-field-mapping-fix.md) - 非诉讼表字段映射问题修复
- [JPA配置问题分析](jpa-config-issue-analysis.md) - JPA多数据源配置问题分析
- [用户系统EntityManager创建问题](userSystemEntityManagerFactory-bean-creation-issue.md) - 用户系统数据源配置问题

## 🔍 问题诊断步骤

### 1. 收集信息
- 查看错误日志
- 记录错误时间
- 确认操作步骤
- 检查环境配置

### 2. 基础检查
- 验证系统要求
- 检查服务状态
- 确认配置文件
- 测试网络连接

### 3. 深入分析
- 分析错误堆栈
- 检查相关组件
- 验证数据完整性
- 测试隔离环境

### 4. 解决方案
- 应用修复措施
- 验证问题解决
- 记录解决过程
- 更新文档

## 📞 获取帮助

### 内部资源
- [项目完整指南](../guides/README.md)
- [AI助手工作指南](../guides/ai-assistant-guide.md)
- [数据库迁移记录](../operations/database-migration.md)

### 联系方式
- **项目负责人**: Zhou Libing
- **技术支持**: AI Assistant (Augment Agent)
- **紧急联系**: 查看项目README.md

## 📝 贡献指南

如果您遇到新的问题并找到了解决方案，请：

1. **创建问题文档**: 在此目录下创建新的markdown文件
2. **文档命名**: 使用描述性名称，如 `backend-startup-error-fix.md`
3. **内容结构**: 包含问题描述、原因分析、解决步骤
4. **更新索引**: 在本README中添加链接

### 文档模板
```markdown
# 问题标题

## 问题描述
详细描述遇到的问题

## 错误信息
```
错误日志或错误信息
```

## 原因分析
分析问题的根本原因

## 解决步骤
1. 步骤一
2. 步骤二
3. 步骤三

## 验证方法
如何验证问题已解决

## 预防措施
如何避免问题再次发生
```

---

**最后更新**: 2025-08-03
**维护者**: FinancialSystem开发团队
